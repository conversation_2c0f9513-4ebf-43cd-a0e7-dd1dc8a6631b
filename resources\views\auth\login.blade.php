@extends('layouts.app')

@section('title', 'Login - Sistem Kursus')

@section('content')
<div class="min-vh-100 d-flex align-items-center" style="background: #F8FAFC;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <!-- Header -->
                    <div class="card-header text-center py-4" style="background: #075B5E; border: none; border-radius: 16px 16px 0 0;">
                        <div class="mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-white" style="width: 60px; height: 60px;">
                                <i class="fas fa-graduation-cap fa-lg" style="color: #FF3F33;"></i>
                            </div>
                        </div>
                        <h4 class="text-white fw-600 mb-2" style="font-family: 'Poppins', sans-serif; font-size: 1.25rem;">Selamat Datang</h4>
                        <p class="text-white mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem; opacity: 0.8;">Masuk ke akun Sistem Kursus Anda</p>
                    </div>

                    <div class="card-body p-4">
                        @if ($errors->any())
                            <div class="alert border-0 mb-4" style="background: #FFE6E1; color: #7F1D1D; border-radius: 12px;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-circle me-2" style="color: #FF3F33;"></i>
                                    <div style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        @foreach ($errors->all() as $error)
                                            <div>{{ $error }}</div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif

                        <form action="{{ route('login') }}" method="POST">
                            @csrf

                            <div class="mb-3">
                                <label for="email" class="form-label fw-500 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                <input type="email"
                                       class="form-control @error('email') is-invalid @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"
                                       placeholder="Masukkan email Anda"
                                       style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"
                                       required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label fw-500 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <div class="position-relative">
                                    <input type="password"
                                           class="form-control @error('password') is-invalid @enderror"
                                           id="password"
                                           name="password"
                                           placeholder="Masukkan password Anda"
                                           style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"
                                           required>
                                    <button type="button" class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                            onclick="togglePassword()" style="border: none; background: none;">
                                        <i class="fas fa-eye" id="toggleIcon" style="color: #075B5E;"></i>
                                    </button>
                                </div>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember" name="remember"
                                           style="border-radius: 6px;">
                                    <label class="form-check-label text-muted" for="remember" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                        Ingat saya
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn fw-500"
                                        style="background: #FF3F33; border: none; border-radius: 12px; padding: 0.75rem 1rem; color: white; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <i class="fas fa-sign-in-alt me-2"></i>Masuk ke Akun
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                Belum punya akun?
                                <a href="{{ route('register') }}" class="text-decoration-none fw-500"
                                   style="color: #FF3F33;">
                                    Daftar sekarang
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Add focus effects
document.querySelectorAll('.form-control').forEach(input => {
    input.addEventListener('focus', function() {
        this.style.borderColor = '#3674B5';
        this.style.boxShadow = '0 0 0 0.2rem rgba(54, 116, 181, 0.25)';
    });

    input.addEventListener('blur', function() {
        this.style.borderColor = '#E5E7EB';
        this.style.boxShadow = 'none';
    });
});

// Button hover effect
document.querySelector('button[type="submit"]').addEventListener('mouseenter', function() {
    this.style.background = '#578FCA';
});

document.querySelector('button[type="submit"]').addEventListener('mouseleave', function() {
    this.style.background = '#3674B5';
});
</script>
@endsection
