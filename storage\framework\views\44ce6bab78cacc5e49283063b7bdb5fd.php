<?php $__env->startSection('title', 'Detail Pengguna - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-600 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.75rem;">
                <i class="fas fa-user me-2"></i>Detail Pengguna
            </h1>
            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Informasi lengkap pengguna <?php echo e($user->name); ?></p>
        </div>
        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn fw-500"
           style="background: #FFE6E1; color: #FF3F33; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: 2px solid #FF3F33;">
            <i class="fas fa-arrow-left me-1"></i>Kembali
        </a>
    </div>

    <div class="row">
        <!-- User Profile Card -->
        <div class="col-md-4 mb-4">
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-body text-center p-4">
                    <div class="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 100px; height: 100px; background: linear-gradient(135deg, #FF3F33, #FFE6E1); color: white; font-weight: 600; font-size: 2rem;">
                        <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                    </div>
                    <h4 class="fw-600 mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;"><?php echo e($user->name); ?></h4>
                    <p class="text-muted mb-3" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;"><?php echo e($user->email); ?></p>
                    
                    <?php if($user->role): ?>
                        <?php if($user->role->name == 'Admin'): ?>
                            <span class="badge fw-500 mb-3" style="background: #FF3F33; color: white; padding: 0.5rem 1rem; border-radius: 12px; font-size: 0.85rem;">
                                <i class="fas fa-user-shield me-1"></i><?php echo e($user->role->name); ?>

                            </span>
                        <?php elseif($user->role->name == 'Teacher'): ?>
                            <span class="badge fw-500 mb-3" style="background: #9FC87E; color: white; padding: 0.5rem 1rem; border-radius: 12px; font-size: 0.85rem;">
                                <i class="fas fa-chalkboard-teacher me-1"></i><?php echo e($user->role->name); ?>

                            </span>
                        <?php else: ?>
                            <span class="badge fw-500 mb-3" style="background: #075B5E; color: white; padding: 0.5rem 1rem; border-radius: 12px; font-size: 0.85rem;">
                                <i class="fas fa-user-graduate me-1"></i><?php echo e($user->role->name); ?>

                            </span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <div class="row text-center mt-4">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="fw-600 mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                    <?php if($user->isTeacher()): ?>
                                        <?php echo e($user->taughtCourses->count()); ?>

                                    <?php else: ?>
                                        <?php echo e($user->enrollments->count()); ?>

                                    <?php endif; ?>
                                </h5>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    <?php if($user->isTeacher()): ?>
                                        Kursus Diajar
                                    <?php else: ?>
                                        Kursus Diikuti
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="fw-600 mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                <?php echo e($user->payments->count()); ?>

                            </h5>
                            <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">Pembayaran</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Info Card -->
            <div class="card border-0 mt-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-info-circle me-2"></i>Informasi
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="mb-3">
                        <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">EMAIL</label>
                        <p class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"><?php echo e($user->email); ?></p>
                    </div>
                    <?php if($user->phone): ?>
                        <div class="mb-3">
                            <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">TELEPON</label>
                            <p class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"><?php echo e($user->phone); ?></p>
                        </div>
                    <?php endif; ?>
                    <div class="mb-3">
                        <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">BERGABUNG</label>
                        <p class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"><?php echo e($user->created_at->format('d M Y H:i')); ?></p>
                    </div>
                    <div class="mb-0">
                        <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">STATUS</label>
                        <div>
                            <?php if($user->email_verified_at): ?>
                                <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                    <i class="fas fa-check me-1"></i>Aktif
                                </span>
                            <?php else: ?>
                                <span class="badge fw-500" style="background: #FF3F33; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                    <i class="fas fa-times me-1"></i>Nonaktif
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Cards -->
        <div class="col-md-8">
            <?php if($user->isTeacher()): ?>
                <!-- Courses Taught -->
                <div class="card border-0 mb-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                        <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                            <i class="fas fa-chalkboard-teacher me-2"></i>Kursus yang Diajar
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php $__empty_1 = true; $__currentLoopData = $user->taughtCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="d-flex align-items-center p-4 border-bottom">
                                <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 50px; height: 50px; background: #9FC87E; color: white;">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"><?php echo e($course->title); ?></h6>
                                    <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                        <?php echo e($course->enrollments->count()); ?> siswa • <?php echo e($course->formatted_price); ?>

                                    </small>
                                </div>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    <?php echo e($course->created_at->format('M Y')); ?>

                                </small>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-chalkboard-teacher fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada kursus yang diajar</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Enrollments -->
                <div class="card border-0 mb-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                        <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                            <i class="fas fa-user-graduate me-2"></i>Kursus yang Diikuti
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php $__empty_1 = true; $__currentLoopData = $user->enrollments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="d-flex align-items-center p-4 border-bottom">
                                <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 50px; height: 50px; background: #075B5E; color: white;">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"><?php echo e($enrollment->course->title); ?></h6>
                                    <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                        Pengajar: <?php echo e($enrollment->course->instructor->name); ?>

                                    </small>
                                </div>
                                <div class="text-end">
                                    <?php if($enrollment->status == 'active'): ?>
                                        <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                            Aktif
                                        </span>
                                    <?php elseif($enrollment->status == 'completed'): ?>
                                        <span class="badge fw-500" style="background: #075B5E; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                            Selesai
                                        </span>
                                    <?php else: ?>
                                        <span class="badge fw-500" style="background: #FF3F33; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                            Dibatalkan
                                        </span>
                                    <?php endif; ?>
                                    <br><small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.7rem;">
                                        <?php echo e($enrollment->enrolled_at->format('d M Y')); ?>

                                    </small>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-user-graduate fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum mengikuti kursus apapun</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Payments -->
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-credit-card me-2"></i>Riwayat Pembayaran
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php $__empty_1 = true; $__currentLoopData = $user->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="d-flex align-items-center p-4 border-bottom">
                            <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px; background: #6C5CE7; color: white;">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;"><?php echo e($payment->course->title); ?></h6>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    <?php echo e($payment->payment_method); ?> • Rp <?php echo e(number_format($payment->amount, 0, ',', '.')); ?>

                                </small>
                            </div>
                            <div class="text-end">
                                <?php if($payment->status == 'verified'): ?>
                                    <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                        Verified
                                    </span>
                                <?php elseif($payment->status == 'pending'): ?>
                                    <span class="badge fw-500" style="background: #FFA500; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                        Pending
                                    </span>
                                <?php else: ?>
                                    <span class="badge fw-500" style="background: #FF3F33; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                        Rejected
                                    </span>
                                <?php endif; ?>
                                <br><small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.7rem;">
                                    <?php echo e($payment->created_at->format('d M Y')); ?>

                                </small>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada riwayat pembayaran</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views\admin\users\show.blade.php ENDPATH**/ ?>