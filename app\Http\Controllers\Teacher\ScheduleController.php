<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Schedule;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ScheduleController extends Controller
{
    /**
     * Display a listing of schedules for teacher's courses.
     */
    public function index(Request $request)
    {
        $query = Schedule::whereHas('course', function($q) {
            $q->where('instructor_id', Auth::id());
        })->with('course');

        // Filter by course if specified
        if ($request->filled('course_id')) {
            $query->where('course_id', $request->course_id);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->where('date', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->where('date', '<=', $request->end_date);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $schedules = $query->orderBy('date', 'desc')
            ->orderBy('start_time', 'desc')
            ->paginate(15);

        $courses = Course::where('instructor_id', Auth::id())->orderBy('title')->get();

        return view('teacher.schedules.index', compact('schedules', 'courses'));
    }

    /**
     * Show the form for creating a new schedule.
     */
    public function create(Request $request)
    {
        $courseId = $request->get('course_id');
        $courses = Course::where('instructor_id', Auth::id())->orderBy('title')->get();
        
        $selectedCourse = null;
        if ($courseId) {
            $selectedCourse = Course::where('instructor_id', Auth::id())
                ->where('id', $courseId)
                ->first();
        }

        return view('teacher.schedules.create', compact('courses', 'selectedCourse'));
    }

    /**
     * Store a newly created schedule in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'location' => 'nullable|string|max:255',
            'type' => 'required|in:online,offline,hybrid',
            'meeting_link' => 'nullable|url|required_if:type,online,hybrid',
        ]);

        // Verify that the course belongs to the authenticated teacher
        $course = Course::where('id', $request->course_id)
            ->where('instructor_id', Auth::id())
            ->firstOrFail();

        // Check for schedule conflicts
        $conflictingSchedule = Schedule::where('course_id', $request->course_id)
            ->where('date', $request->date)
            ->where(function($query) use ($request) {
                $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                    ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                    ->orWhere(function($q) use ($request) {
                        $q->where('start_time', '<=', $request->start_time)
                          ->where('end_time', '>=', $request->end_time);
                    });
            })
            ->first();

        if ($conflictingSchedule) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Jadwal bertabrakan dengan jadwal yang sudah ada pada tanggal dan waktu tersebut.');
        }

        Schedule::create([
            'course_id' => $request->course_id,
            'title' => $request->title,
            'description' => $request->description,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'location' => $request->location,
            'type' => $request->type,
            'meeting_link' => $request->meeting_link,
        ]);

        return redirect()->route('teacher.schedules.index')
            ->with('success', 'Jadwal berhasil ditambahkan!');
    }

    /**
     * Display the specified schedule.
     */
    public function show(Schedule $schedule)
    {
        // Verify that the schedule belongs to teacher's course
        if ($schedule->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        return view('teacher.schedules.show', compact('schedule'));
    }

    /**
     * Show the form for editing the specified schedule.
     */
    public function edit(Schedule $schedule)
    {
        // Verify that the schedule belongs to teacher's course
        if ($schedule->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $courses = Course::where('instructor_id', Auth::id())->orderBy('title')->get();

        return view('teacher.schedules.edit', compact('schedule', 'courses'));
    }

    /**
     * Update the specified schedule in storage.
     */
    public function update(Request $request, Schedule $schedule)
    {
        // Verify that the schedule belongs to teacher's course
        if ($schedule->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'location' => 'nullable|string|max:255',
            'type' => 'required|in:online,offline,hybrid',
            'meeting_link' => 'nullable|url|required_if:type,online,hybrid',
        ]);

        // Verify that the new course belongs to the authenticated teacher
        $course = Course::where('id', $request->course_id)
            ->where('instructor_id', Auth::id())
            ->firstOrFail();

        // Check for schedule conflicts (excluding current schedule)
        $conflictingSchedule = Schedule::where('course_id', $request->course_id)
            ->where('id', '!=', $schedule->id)
            ->where('date', $request->date)
            ->where(function($query) use ($request) {
                $query->whereBetween('start_time', [$request->start_time, $request->end_time])
                    ->orWhereBetween('end_time', [$request->start_time, $request->end_time])
                    ->orWhere(function($q) use ($request) {
                        $q->where('start_time', '<=', $request->start_time)
                          ->where('end_time', '>=', $request->end_time);
                    });
            })
            ->first();

        if ($conflictingSchedule) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Jadwal bertabrakan dengan jadwal yang sudah ada pada tanggal dan waktu tersebut.');
        }

        $schedule->update([
            'course_id' => $request->course_id,
            'title' => $request->title,
            'description' => $request->description,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'location' => $request->location,
            'type' => $request->type,
            'meeting_link' => $request->meeting_link,
        ]);

        return redirect()->route('teacher.schedules.index')
            ->with('success', 'Jadwal berhasil diperbarui!');
    }

    /**
     * Remove the specified schedule from storage.
     */
    public function destroy(Schedule $schedule)
    {
        // Verify that the schedule belongs to teacher's course
        if ($schedule->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $schedule->delete();

        return redirect()->route('teacher.schedules.index')
            ->with('success', 'Jadwal berhasil dihapus!');
    }

    /**
     * Get calendar view of schedules.
     */
    public function calendar(Request $request)
    {
        $schedules = Schedule::whereHas('course', function($q) {
            $q->where('instructor_id', Auth::id());
        })->with('course')->get();

        $courses = Course::where('instructor_id', Auth::id())->orderBy('title')->get();

        return view('teacher.schedules.calendar', compact('schedules', 'courses'));
    }
}
