<?php $__env->startSection('title', $course->title . ' - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Course Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <nav aria-label="breadcrumb" class="mb-3">
                                <ol class="breadcrumb text-white-50">
                                    <li class="breadcrumb-item">
                                        <a href="<?php echo e(route('courses.index')); ?>" class="text-white-50 text-decoration-none">
                                            <i class="fas fa-book me-1"></i>Kursus
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white" aria-current="page">
                                        <?php echo e(Str::limit($course->title, 30)); ?>

                                    </li>
                                </ol>
                            </nav>
                            <h1 class="h2 mb-2"><?php echo e($course->title); ?></h1>
                            <p class="mb-2">
                                <i class="fas fa-user me-2"></i>
                                Pengajar: <strong><?php echo e($course->instructor->name ?? 'Belum ditentukan'); ?></strong>
                            </p>
                            <div class="d-flex align-items-center">
                                <?php if($course->reviews_count > 0): ?>
                                    <div class="text-warning me-3">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo e($i <= $course->average_rating ? '' : '-o'); ?>"></i>
                                        <?php endfor; ?>
                                        <span class="ms-1"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                                    </div>
                                    <small class="text-white-50">
                                        (<?php echo e($course->reviews_count); ?> review<?php echo e($course->reviews_count > 1 ? 's' : ''); ?>)
                                    </small>
                                <?php else: ?>
                                    <small class="text-white-50">Belum ada review</small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <span class="badge bg-light text-primary fs-4 px-3 py-2">
                                    <?php echo e($course->formatted_price); ?>

                                </span>
                            </div>
                            <small class="text-white-50">
                                <i class="fas fa-users me-1"></i>
                                <?php echo e($course->total_students); ?> siswa terdaftar
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8 mb-4">
            <!-- Course Description -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Deskripsi Kursus
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text"><?php echo e($course->description); ?></p>
                </div>
            </div>

            <!-- Course Materials -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Materi Pembelajaran
                    </h5>
                    <span class="badge bg-primary"><?php echo e($course->materials->count()); ?> materi</span>
                </div>
                <div class="card-body">
                    <?php $__empty_1 = true; $__currentLoopData = $course->materials()->orderBy('order')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $material): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="bg-light rounded-circle p-2 me-3">
                                <i class="fas fa-play text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo e($material->title); ?></h6>
                                <small class="text-muted"><?php echo e(Str::limit($material->content, 100)); ?></small>
                            </div>
                            <div>
                                <span class="badge bg-light text-dark"><?php echo e($material->order); ?></span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-file-alt fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Materi pembelajaran belum tersedia</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Course Schedule -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>Jadwal Kursus
                    </h5>
                    <span class="badge bg-info"><?php echo e($course->schedules->count()); ?> jadwal</span>
                </div>
                <div class="card-body">
                    <?php $__empty_1 = true; $__currentLoopData = $course->schedules()->orderBy('date')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="bg-light rounded-circle p-2 me-3">
                                <i class="fas fa-clock text-info"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo e($schedule->title); ?></h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i><?php echo e($schedule->date->format('d M Y')); ?>

                                    <i class="fas fa-clock ms-2 me-1"></i><?php echo e($schedule->start_time); ?> - <?php echo e($schedule->end_time); ?>

                                </small>
                                <?php if($schedule->location): ?>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i><?php echo e($schedule->location); ?>

                                    </small>
                                <?php endif; ?>
                            </div>
                            <div>
                                <span class="badge bg-<?php echo e($schedule->type == 'online' ? 'success' : 'primary'); ?>">
                                    <?php echo e(ucfirst($schedule->type)); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-calendar fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Jadwal kursus belum tersedia</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Reviews Section -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Review & Rating
                    </h5>
                    <?php if(auth()->guard()->check()): ?>
                        <?php if(auth()->user()->enrollments()->where('course_id', $course->id)->exists() && 
                            !auth()->user()->reviews()->where('course_id', $course->id)->exists()): ?>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#reviewModal">
                                <i class="fas fa-plus me-1"></i>Tulis Review
                            </button>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php $__empty_1 = true; $__currentLoopData = $course->reviews()->with('user')->latest()->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="mb-3 pb-3 border-bottom">
                            <div class="d-flex align-items-center mb-2">
                                <div class="bg-primary rounded-circle p-2 me-3">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0"><?php echo e($review->user->name); ?></h6>
                                    <div class="text-warning">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star<?php echo e($i <= $review->rating ? '' : '-o'); ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                <small class="text-muted"><?php echo e($review->created_at->diffForHumans()); ?></small>
                            </div>
                            <?php if($review->review): ?>
                                <p class="mb-0 text-muted"><?php echo e($review->review); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-star fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Belum ada review untuk kursus ini</p>
                        </div>
                    <?php endif; ?>

                    <?php if($course->reviews_count > 5): ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('courses.reviews.index', $course)); ?>" class="btn btn-outline-primary">
                                Lihat Semua Review (<?php echo e($course->reviews_count); ?>)
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Enrollment Card -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <?php if(auth()->guard()->check()): ?>
                        <?php if(auth()->user()->isStudent()): ?>
                            <?php
                                $isEnrolled = auth()->user()->enrollments()->where('course_id', $course->id)->exists();
                                $hasPaid = auth()->user()->payments()->where('course_id', $course->id)->where('status', 'verified')->exists();
                            ?>

                            <?php if($isEnrolled): ?>
                                <?php if($hasPaid): ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Anda sudah terdaftar dan telah membayar kursus ini
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-clock me-2"></i>
                                        Anda sudah terdaftar. Silakan lakukan pembayaran.
                                    </div>
                                    <a href="<?php echo e(route('student.payments.create', $course)); ?>" class="btn btn-warning w-100">
                                        <i class="fas fa-credit-card me-2"></i>Bayar Sekarang
                                    </a>
                                <?php endif; ?>
                            <?php else: ?>
                                <h5 class="text-primary mb-3"><?php echo e($course->formatted_price); ?></h5>
                                <form action="<?php echo e(route('student.courses.enroll', $course)); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                        <i class="fas fa-user-plus me-2"></i>Daftar Kursus
                                    </button>
                                </form>
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Garansi 30 hari uang kembali
                                </small>
                            <?php endif; ?>
                        <?php elseif(auth()->user()->isTeacher()): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Anda adalah pengajar. Pengajar tidak dapat mendaftar sebagai siswa.
                            </div>
                            <button class="btn btn-secondary btn-lg w-100 mb-3" disabled>
                                <i class="fas fa-chalkboard-teacher me-2"></i>Anda Pengajar
                            </button>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Anda adalah admin. Admin tidak dapat mendaftar sebagai siswa.
                            </div>
                            <button class="btn btn-secondary btn-lg w-100 mb-3" disabled>
                                <i class="fas fa-user-shield me-2"></i>Anda Admin
                            </button>
                        <?php endif; ?>
                    <?php else: ?>
                        <h5 class="text-primary mb-3"><?php echo e($course->formatted_price); ?></h5>
                        <a href="<?php echo e(route('login')); ?>" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i>Login untuk Daftar
                        </a>
                        <small class="text-muted">
                            Belum punya akun? 
                            <a href="<?php echo e(route('register')); ?>" class="text-primary">Daftar di sini</a>
                        </small>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Course Info -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info me-2"></i>Informasi Kursus
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h6><?php echo e($course->total_students); ?></h6>
                            <small class="text-muted">Siswa</small>
                        </div>
                        <div class="col-6 mb-3">
                            <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                            <h6><?php echo e($course->materials->count()); ?></h6>
                            <small class="text-muted">Materi</small>
                        </div>
                        <div class="col-6">
                            <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                            <h6><?php echo e($course->schedules->count()); ?></h6>
                            <small class="text-muted">Jadwal</small>
                        </div>
                        <div class="col-6">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h6><?php echo e(number_format($course->average_rating, 1)); ?></h6>
                            <small class="text-muted">Rating</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Review Modal -->
<?php if(auth()->guard()->check()): ?>
<?php if(auth()->user()->enrollments()->where('course_id', $course->id)->exists() && 
    !auth()->user()->reviews()->where('course_id', $course->id)->exists()): ?>
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-star me-2"></i>Tulis Review
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('student.courses.review', $course)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Rating</label>
                        <div class="rating-input">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <input type="radio" name="rating" value="<?php echo e($i); ?>" id="star<?php echo e($i); ?>" required>
                                <label for="star<?php echo e($i); ?>" class="star">
                                    <i class="fas fa-star"></i>
                                </label>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="review" class="form-label">Review (Opsional)</label>
                        <textarea class="form-control" id="review" name="review" rows="4" 
                                  placeholder="Bagikan pengalaman Anda mengikuti kursus ini..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>Kirim Review
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>

<style>
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #ddd;
    transition: color 0.2s;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #ffc107;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/courses/show.blade.php ENDPATH**/ ?>