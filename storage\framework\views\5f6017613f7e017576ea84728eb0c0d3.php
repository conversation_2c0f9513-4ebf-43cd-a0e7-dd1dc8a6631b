<?php $__env->startSection('title', 'Kelo<PERSON> Siswa - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #FF3F33; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-users me-2"></i><PERSON><PERSON><PERSON> Siswa
                    </h2>
                    <p class="text-muted mb-0">Ke<PERSON>la siswa yang terdaftar dalam kursus Anda</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: linear-gradient(135deg, #FF3F33 0%, #FFE6E1 100%);">
                <div class="card-body text-white">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 class="mb-1"><?php echo e($stats['total_students']); ?></h4>
                    <small>Total Siswa</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: linear-gradient(135deg, #9FC87E 0%, #e8f5e8 100%);">
                <div class="card-body text-white">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h4 class="mb-1"><?php echo e($stats['active_students']); ?></h4>
                    <small>Siswa Aktif</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: linear-gradient(135deg, #075B5E 0%, #9FC87E 100%);">
                <div class="card-body text-white">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <h4 class="mb-1"><?php echo e($stats['completed_students']); ?></h4>
                    <small>Siswa Lulus</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: linear-gradient(135deg, #6c757d 0%, #f8f9fa 100%);">
                <div class="card-body text-white">
                    <i class="fas fa-user-times fa-2x mb-2"></i>
                    <h4 class="mb-1"><?php echo e($stats['cancelled_students']); ?></h4>
                    <small>Siswa Batal</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="card mb-4" style="border-radius: 15px; border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('teacher.students.index')); ?>">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="course_id" class="form-label small fw-semibold">Kursus</label>
                        <select class="form-select form-select-sm" id="course_id" name="course_id">
                            <option value="">Semua Kursus</option>
                            <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($course->id); ?>" <?php echo e(request('course_id') == $course->id ? 'selected' : ''); ?>>
                                    <?php echo e($course->title); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label small fw-semibold">Status</label>
                        <select class="form-select form-select-sm" id="status" name="status">
                            <option value="">Semua Status</option>
                            <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Aktif</option>
                            <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Selesai</option>
                            <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Dibatalkan</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label small fw-semibold">Cari Siswa</label>
                        <input type="text" class="form-control form-control-sm" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" placeholder="Nama atau email siswa">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2 w-100">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="<?php echo e(route('teacher.students.index')); ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-undo me-1"></i>Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Students List -->
    <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <div class="card-body p-0">
            <?php $__empty_1 = true; $__currentLoopData = $enrollments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="d-flex align-items-center p-4 border-bottom student-item">
                    <div class="me-3">
                        <div class="rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 50px; height: 50px; background: #FFE6E1; color: #FF3F33; font-weight: bold;">
                            <?php echo e(strtoupper(substr($enrollment->user->name, 0, 2))); ?>

                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                <?php echo e($enrollment->user->name); ?>

                            </h6>
                            <span class="badge <?php echo e($enrollment->status == 'active' ? 'bg-success' : ($enrollment->status == 'completed' ? 'bg-primary' : 'bg-secondary')); ?>">
                                <?php echo e(ucfirst($enrollment->status)); ?>

                            </span>
                        </div>
                        <p class="text-muted mb-1 small"><?php echo e($enrollment->user->email); ?></p>
                        <p class="text-muted mb-2 small"><?php echo e($enrollment->course->title); ?></p>
                        <div class="d-flex align-items-center text-muted small">
                            <i class="fas fa-calendar me-1"></i>
                            Daftar: <?php echo e($enrollment->enrolled_at->format('d M Y')); ?>

                            <?php if($enrollment->user->phone): ?>
                                <span class="mx-2">•</span>
                                <i class="fas fa-phone me-1"></i>
                                <?php echo e($enrollment->user->phone); ?>

                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="ms-3">
                        <div class="btn-group" role="group">
                            <a href="<?php echo e(route('teacher.students.show', $enrollment->user)); ?>" 
                               class="btn btn-outline-info btn-sm" title="Lihat Detail">
                                <i class="fas fa-eye"></i>
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown" title="Ubah Status">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <?php if($enrollment->status != 'active'): ?>
                                        <li>
                                            <form action="<?php echo e(route('teacher.enrollments.update-status', $enrollment)); ?>" method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <input type="hidden" name="status" value="active">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-check-circle text-success me-2"></i>Aktifkan
                                                </button>
                                            </form>
                                        </li>
                                    <?php endif; ?>
                                    <?php if($enrollment->status != 'completed'): ?>
                                        <li>
                                            <form action="<?php echo e(route('teacher.enrollments.update-status', $enrollment)); ?>" method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <input type="hidden" name="status" value="completed">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-graduation-cap text-primary me-2"></i>Tandai Selesai
                                                </button>
                                            </form>
                                        </li>
                                    <?php endif; ?>
                                    <?php if($enrollment->status != 'cancelled'): ?>
                                        <li>
                                            <form action="<?php echo e(route('teacher.enrollments.update-status', $enrollment)); ?>" method="POST" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <input type="hidden" name="status" value="cancelled">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-times-circle text-danger me-2"></i>Batalkan
                                                </button>
                                            </form>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-user-slash fa-3x text-muted"></i>
                    </div>
                    <h6 class="text-muted">Belum ada siswa terdaftar</h6>
                    <p class="text-muted small mb-3">Siswa akan muncul di sini setelah mendaftar kursus Anda</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Pagination -->
    <?php if($enrollments->hasPages()): ?>
        <div class="d-flex justify-content-center mt-4">
            <?php echo e($enrollments->links()); ?>

        </div>
    <?php endif; ?>
</div>

<style>
.student-item:hover {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px;
}

.alert {
    border-radius: 12px;
    border: none;
}

.card {
    transition: all 0.3s ease;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/teacher/students/index.blade.php ENDPATH**/ ?>