@extends('layouts.app')

@section('title', 'Laporan - Admin')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-600 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.75rem;">
                <i class="fas fa-chart-bar me-2"></i>Laporan & Analitik
            </h1>
            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Dashboard analitik dan laporan sistem</p>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="card border-0 mb-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
        <div class="card-body p-4">
            <form method="GET" action="{{ route('admin.reports.index') }}">
                <div class="row align-items-end">
                    <div class="col-md-4 mb-3">
                        <label for="start_date" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Tanggal Mulai</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ $startDate->format('Y-m-d') }}"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="end_date" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Tanggal Akhir</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ $endDate->format('Y-m-d') }}"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                    </div>
                    <div class="col-md-4 mb-3">
                        <button type="submit" class="btn fw-500 me-2"
                                style="background: #FF3F33; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                            <i class="fas fa-filter me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.reports.index') }}" class="btn fw-500"
                           style="background: #FFE6E1; color: #FF3F33; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: 2px solid #FF3F33;">
                            <i class="fas fa-undo me-1"></i>Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Overview Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #FFE6E1 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #FF3F33;">
                            <i class="fas fa-users fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #FF3F33; font-family: 'Poppins', sans-serif;">
                        {{ number_format($totalUsers) }}
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total Pengguna</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #E8F5E8 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #9FC87E;">
                            <i class="fas fa-book fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #9FC87E; font-family: 'Poppins', sans-serif;">
                        {{ number_format($totalCourses) }}
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total Kursus</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #E0F2F1 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #075B5E;">
                            <i class="fas fa-user-graduate fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                        {{ number_format($totalEnrollments) }}
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total Pendaftaran</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #F3E5F5 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #6C5CE7;">
                            <i class="fas fa-money-bill fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #6C5CE7; font-family: 'Poppins', sans-serif;">
                        {{ number_format($totalRevenue / 1000000, 1) }}M
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total Pendapatan</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Popular Courses -->
        <div class="col-md-6 mb-4">
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-star me-2"></i>Kursus Populer
                    </h5>
                </div>
                <div class="card-body p-0">
                    @forelse($popularCourses as $course)
                        <div class="d-flex align-items-center p-4 border-bottom">
                            <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 45px; height: 45px; background: #9FC87E; color: white; font-weight: 600; font-size: 0.8rem;">
                                {{ $loop->iteration }}
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $course->title }}</h6>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    {{ $course->instructor->name }}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge fw-500" style="background: #075B5E; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                    {{ $course->enrollments_count }} siswa
                                </span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-book fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada data kursus</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Recent Enrollments -->
        <div class="col-md-6 mb-4">
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-clock me-2"></i>Pendaftaran Terbaru
                    </h5>
                </div>
                <div class="card-body p-0">
                    @forelse($recentEnrollments as $enrollment)
                        <div class="d-flex align-items-center p-4 border-bottom">
                            <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 45px; height: 45px; background: linear-gradient(135deg, #FF3F33, #FFE6E1); color: white; font-weight: 600; font-size: 0.8rem;">
                                {{ strtoupper(substr($enrollment->user->name, 0, 1)) }}
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $enrollment->user->name }}</h6>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    {{ $enrollment->course->title }}
                                </small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.7rem;">
                                    {{ $enrollment->enrolled_at->diffForHumans() }}
                                </small>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-user-graduate fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada pendaftaran</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Course Performance -->
        <div class="col-md-12 mb-4">
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-chart-line me-2"></i>Performa Kursus
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" style="font-family: 'Poppins', sans-serif;">
                            <thead style="background: #F8FAFC;">
                                <tr>
                                    <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Kursus</th>
                                    <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Pengajar</th>
                                    <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Siswa</th>
                                    <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Pendapatan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($coursePerformance as $course)
                                    <tr style="border-bottom: 1px solid #F1F5F9;">
                                        <td class="align-middle py-3 px-4">
                                            <h6 class="mb-1 fw-600" style="color: #075B5E; font-size: 0.9rem;">{{ $course->title }}</h6>
                                            <small class="text-muted" style="font-size: 0.8rem;">{{ $course->formatted_price }}</small>
                                        </td>
                                        <td class="align-middle py-3 px-4">
                                            <span style="color: #075B5E; font-size: 0.9rem;">{{ $course->instructor->name }}</span>
                                        </td>
                                        <td class="align-middle py-3 px-4">
                                            <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                                {{ $course->enrollments_count }}
                                            </span>
                                        </td>
                                        <td class="align-middle py-3 px-4">
                                            <span class="fw-600" style="color: #6C5CE7; font-size: 0.9rem;">
                                                Rp {{ number_format($course->payments_sum_amount ?? 0, 0, ',', '.') }}
                                            </span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center py-4">
                                            <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada data performa</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-credit-card me-2"></i>Pembayaran Terbaru
                    </h5>
                </div>
                <div class="card-body p-0">
                    @forelse($recentPayments as $payment)
                        <div class="d-flex align-items-center p-4 border-bottom">
                            <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 45px; height: 45px; background: #6C5CE7; color: white;">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $payment->user->name }}</h6>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    {{ $payment->course->title }} • {{ $payment->payment_method }}
                                </small>
                            </div>
                            <div class="text-end">
                                <div class="fw-600" style="color: #6C5CE7; font-size: 0.9rem;">
                                    Rp {{ number_format($payment->amount, 0, ',', '.') }}
                                </div>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.7rem;">
                                    {{ $payment->created_at->diffForHumans() }}
                                </small>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada pembayaran</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
