@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - Admin')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-600 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.75rem;">
                <i class="fas fa-user-graduate me-2"></i>Kelola Pendaftaran
            </h1>
            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Manajemen pendaftaran siswa ke kursus</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #FFE6E1 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #3674B5;">
                            <i class="fas fa-user-check fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #3674B5; font-family: 'Poppins', sans-serif;">
                        {{ $enrollments->where('status', 'active')->count() }}
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Aktif</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #E8F5E8 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #9FC87E;">
                            <i class="fas fa-graduation-cap fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #9FC87E; font-family: 'Poppins', sans-serif;">
                        {{ $enrollments->where('status', 'completed')->count() }}
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Selesai</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #E0F2F1 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #075B5E;">
                            <i class="fas fa-user-times fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                        {{ $enrollments->where('status', 'cancelled')->count() }}
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Dibatalkan</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #F3E5F5 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #6C5CE7;">
                            <i class="fas fa-users fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #6C5CE7; font-family: 'Poppins', sans-serif;">
                        {{ $enrollments->count() }}
                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card border-0 mb-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
        <div class="card-body p-4">
            <form method="GET" action="{{ route('admin.enrollments.index') }}">
                <div class="row align-items-end">
                    <div class="col-md-4 mb-3">
                        <label for="status" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Status</label>
                        <select class="form-select" id="status" name="status" style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                            <option value="">Semua Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="search" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Cari</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Nama siswa atau kursus..."
                               style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                    </div>
                    <div class="col-md-4 mb-3">
                        <button type="submit" class="btn fw-500 me-2"
                                style="background: #3674B5; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.enrollments.index') }}" class="btn fw-500"
                           style="background: #FFE6E1; color: #3674B5; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: 2px solid #3674B5;">
                            <i class="fas fa-undo me-1"></i>Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Enrollments Table -->
    <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
        <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
            <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                <i class="fas fa-list me-2"></i>Daftar Pendaftaran
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" style="font-family: 'Poppins', sans-serif;">
                    <thead style="background: #F8FAFC;">
                        <tr>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Siswa</th>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Kursus</th>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Tanggal Daftar</th>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Status</th>
                            <th class="border-0 py-3 px-4 text-center" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($enrollments as $enrollment)
                            <tr style="border-bottom: 1px solid #F1F5F9;">
                                <td class="align-middle py-3 px-4">
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                             style="width: 45px; height: 45px; background: linear-gradient(135deg, #3674B5, #FFE6E1); color: white; font-weight: 600; font-size: 0.9rem;">
                                            {{ strtoupper(substr($enrollment->user->name, 0, 1)) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-600" style="color: #075B5E; font-size: 0.9rem;">{{ $enrollment->user->name }}</h6>
                                            <small class="text-muted" style="font-size: 0.8rem;">{{ $enrollment->user->email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="align-middle py-3 px-4">
                                    <h6 class="mb-1 fw-500" style="color: #075B5E; font-size: 0.9rem;">{{ $enrollment->course->title }}</h6>
                                    <small class="text-muted" style="font-size: 0.8rem;">
                                        <i class="fas fa-user me-1"></i>{{ $enrollment->course->instructor->name }}
                                    </small>
                                </td>
                                <td class="align-middle py-3 px-4">
                                    <div style="font-size: 0.9rem; color: #075B5E; font-weight: 500;">
                                        {{ $enrollment->enrolled_at->format('d M Y') }}
                                    </div>
                                    <small class="text-muted" style="font-size: 0.8rem;">{{ $enrollment->enrolled_at->format('H:i') }}</small>
                                </td>
                                <td class="align-middle py-3 px-4">
                                    @if($enrollment->status == 'active')
                                        <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                            <i class="fas fa-check me-1"></i>Aktif
                                        </span>
                                    @elseif($enrollment->status == 'completed')
                                        <span class="badge fw-500" style="background: #075B5E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                            <i class="fas fa-graduation-cap me-1"></i>Selesai
                                        </span>
                                    @else
                                        <span class="badge fw-500" style="background: #3674B5; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                            <i class="fas fa-times me-1"></i>Dibatalkan
                                        </span>
                                    @endif
                                </td>
                                <td class="align-middle text-center py-3 px-4">
                                    <div class="btn-group" role="group">
                                        @if($enrollment->status == 'active')
                                            <button class="btn btn-sm me-1" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#completeModal{{ $enrollment->id }}"
                                                    title="Tandai Selesai"
                                                    style="background: #9FC87E; color: white; border-radius: 8px; padding: 0.4rem 0.6rem; border: none;">
                                                <i class="fas fa-graduation-cap"></i>
                                            </button>
                                            <button class="btn btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#cancelModal{{ $enrollment->id }}"
                                                    title="Batalkan"
                                                    style="background: #FFE6E1; color: #3674B5; border-radius: 8px; padding: 0.4rem 0.6rem; border: 1px solid #3674B5;">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @else
                                            <span class="text-muted" style="font-size: 0.8rem;">-</span>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center mb-3"
                                             style="width: 80px; height: 80px; background: #F8FAFC; color: #9CA3AF;">
                                            <i class="fas fa-user-graduate fa-2x"></i>
                                        </div>
                                        <h5 class="text-muted fw-500" style="font-family: 'Poppins', sans-serif;">Tidak Ada Pendaftaran</h5>
                                        <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada siswa yang mendaftar kursus</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    @if($enrollments->hasPages())
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $enrollments->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Modals for status updates -->
@foreach($enrollments as $enrollment)
    @if($enrollment->status == 'active')
        <!-- Complete Modal -->
        <div class="modal fade" id="completeModal{{ $enrollment->id }}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-graduation-cap me-2" style="color: #9FC87E;"></i>
                            Tandai Selesai
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="{{ route('admin.enrollments.update', $enrollment) }}" method="POST">
                        @csrf
                        @method('PATCH')
                        <input type="hidden" name="status" value="completed">
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert border-0" style="background: #E8F5E8; color: #2D5016; border-radius: 12px;">
                                <h6 class="fw-600" style="font-family: 'Poppins', sans-serif;">Detail Pendaftaran:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <li>Siswa: {{ $enrollment->user->name }}</li>
                                    <li>Kursus: {{ $enrollment->course->title }}</li>
                                    <li>Tanggal Daftar: {{ $enrollment->enrolled_at->format('d M Y H:i') }}</li>
                                </ul>
                            </div>
                            <p class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                Apakah Anda yakin ingin menandai pendaftaran ini sebagai selesai?
                            </p>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn fw-500" data-bs-dismiss="modal"
                                    style="background: #F8FAFC; color: #6B7280; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                Batal
                            </button>
                            <button type="submit" class="btn fw-500"
                                    style="background: #9FC87E; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                <i class="fas fa-graduation-cap me-1"></i>Tandai Selesai
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Cancel Modal -->
        <div class="modal fade" id="cancelModal{{ $enrollment->id }}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-times-circle me-2" style="color: #3674B5;"></i>
                            Batalkan Pendaftaran
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="{{ route('admin.enrollments.update', $enrollment) }}" method="POST">
                        @csrf
                        @method('PATCH')
                        <input type="hidden" name="status" value="cancelled">
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert border-0" style="background: #FFE6E1; color: #7F1D1D; border-radius: 12px;">
                                <h6 class="fw-600" style="font-family: 'Poppins', sans-serif;">Detail Pendaftaran:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <li>Siswa: {{ $enrollment->user->name }}</li>
                                    <li>Kursus: {{ $enrollment->course->title }}</li>
                                    <li>Tanggal Daftar: {{ $enrollment->enrolled_at->format('d M Y H:i') }}</li>
                                </ul>
                            </div>
                            <p class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                Apakah Anda yakin ingin membatalkan pendaftaran ini?
                            </p>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn fw-500" data-bs-dismiss="modal"
                                    style="background: #F8FAFC; color: #6B7280; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                Batal
                            </button>
                            <button type="submit" class="btn fw-500"
                                    style="background: #3674B5; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                <i class="fas fa-times me-1"></i>Batalkan Pendaftaran
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endforeach
@endsection
