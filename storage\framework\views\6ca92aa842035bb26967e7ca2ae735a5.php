<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="mb-2">
                <i class="fas fa-credit-card me-2 text-primary"></i>Kelola Pembayaran
            </h1>
            <p class="text-muted mb-0">Verifikasi dan kelola pembayaran kursus</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-warning text-white">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4><?php echo e($payments->where('status', 'pending')->count()); ?></h4>
                    <small>Menunggu Verifikasi</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-success text-white">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4><?php echo e($payments->where('status', 'verified')->count()); ?></h4>
                    <small>Terverifikasi</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-danger text-white">
                <div class="card-body">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h4><?php echo e($payments->where('status', 'rejected')->count()); ?></h4>
                    <small>Ditolak</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center bg-info text-white">
                <div class="card-body">
                    <i class="fas fa-money-bill fa-2x mb-2"></i>
                    <h4><?php echo e(number_format($payments->where('status', 'verified')->sum('amount') / 1000000, 1)); ?>M</h4>
                    <small>Total Pendapatan</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.payments.index')); ?>">
                <div class="row align-items-end">
                    <div class="col-md-4 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Semua Status</option>
                            <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="verified" <?php echo e(request('status') == 'verified' ? 'selected' : ''); ?>>Verified</option>
                            <option value="rejected" <?php echo e(request('status') == 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="search" class="form-label">Cari</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" placeholder="Nama siswa atau kursus...">
                    </div>
                    <div class="col-md-4 mb-3">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('admin.payments.index')); ?>" class="btn btn-outline-secondary">" class="btn fw-500"
                           style="background: #FFE6E1; color: #3674B5; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: 2px solid #3674B5;">
                            <i class="fas fa-undo me-1"></i>Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Daftar Pembayaran
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Siswa</th>
                            <th>Kursus</th>
                            <th>Jumlah</th>
                            <th>Metode</th>
                            <th>Status</th>
                            <th>Tanggal</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-primary rounded-circle p-2 me-2">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($payment->user->name); ?></h6>
                                            <small class="text-muted"><?php echo e($payment->user->email); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <h6 class="mb-0"><?php echo e($payment->course->title); ?></h6>
                                    <small class="text-muted"><?php echo e($payment->course->formatted_price); ?></small>
                                </td>
                                <td>
                                    <strong class="text-primary">
                                        Rp <?php echo e(number_format($payment->amount, 0, ',', '.')); ?>

                                    </strong>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">
                                        <?php echo e($payment->payment_method); ?>

                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($payment->status == 'verified' ? 'success' : ($payment->status == 'pending' ? 'warning' : 'danger')); ?>">
                                        <?php if($payment->status == 'verified'): ?>
                                            <i class="fas fa-check me-1"></i>Verified
                                        <?php elseif($payment->status == 'pending'): ?>
                                            <i class="fas fa-clock me-1"></i>Pending
                                        <?php else: ?>
                                            <i class="fas fa-times me-1"></i>Rejected
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo e($payment->created_at->format('d M Y')); ?></div>
                                    <small class="text-muted"><?php echo e($payment->created_at->format('H:i')); ?></small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <?php if($payment->proof_file): ?>
                                            <a href="<?php echo e(route('admin.payments.download', $payment)); ?>" 
                                               class="btn btn-outline-primary btn-sm" title="Download Bukti">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if($payment->status == 'pending'): ?>
                                            <button class="btn btn-outline-success btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#verifyModal<?php echo e($payment->id); ?>"
                                                    title="Verifikasi">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#rejectModal<?php echo e($payment->id); ?>"
                                                    title="Tolak">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Tidak Ada Pembayaran</h5>
                                    <p class="text-muted mb-0">Belum ada pembayaran yang perlu diverifikasi</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <?php if($payments->hasPages()): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    <?php echo e($payments->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Payment Verification Modals -->
<?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if($payment->status == 'pending'): ?>
        <!-- Verify Modal -->
        <div class="modal fade" id="verifyModal<?php echo e($payment->id); ?>" tabindex="-1" aria-labelledby="verifyModalLabel<?php echo e($payment->id); ?>" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" id="verifyModalLabel<?php echo e($payment->id); ?>" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-check-circle me-2" style="color: #9FC87E;"></i>
                            Verifikasi Pembayaran
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="<?php echo e(route('admin.payments.verify', $payment)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <input type="hidden" name="status" value="verified">
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert" style="background: #FFE6E1; border: 1px solid #3674B5; border-radius: 12px; color: #075B5E;">
                                <h6 style="color: #075B5E; font-family: 'Poppins', sans-serif; font-weight: 600;">Detail Pembayaran:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif;">
                                    <li>Siswa: <?php echo e($payment->user->name); ?></li>
                                    <li>Kursus: <?php echo e($payment->course->title); ?></li>
                                    <li>Jumlah: Rp <?php echo e(number_format($payment->amount, 0, ',', '.')); ?></li>
                                    <li>Metode: <?php echo e($payment->payment_method); ?></li>
                                </ul>
                            </div>
                            <div class="mb-3">
                                <label for="notes<?php echo e($payment->id); ?>" class="form-label" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-weight: 500;">Catatan (Opsional)</label>
                                <textarea class="form-control" id="notes<?php echo e($payment->id); ?>" name="notes" rows="3"
                                          placeholder="Tambahkan catatan verifikasi..."
                                          style="border-radius: 8px; border: 1px solid #ddd; font-family: 'Poppins', sans-serif;"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                    style="border-radius: 8px; font-family: 'Poppins', sans-serif;">Batal</button>
                            <button type="submit" class="btn"
                                    style="background: #9FC87E; border: none; color: white; border-radius: 8px; font-family: 'Poppins', sans-serif;">
                                <i class="fas fa-check me-1"></i>Verifikasi Pembayaran
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Reject Modal -->
        <div class="modal fade" id="rejectModal<?php echo e($payment->id); ?>" tabindex="-1" aria-labelledby="rejectModalLabel<?php echo e($payment->id); ?>" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" id="rejectModalLabel<?php echo e($payment->id); ?>" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-times-circle me-2" style="color: #3674B5;"></i>
                            Tolak Pembayaran
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="<?php echo e(route('admin.payments.verify', $payment)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <input type="hidden" name="status" value="rejected">
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert" style="background: #FFE6E1; border: 1px solid #3674B5; border-radius: 12px; color: #075B5E;">
                                <h6 style="color: #075B5E; font-family: 'Poppins', sans-serif; font-weight: 600;">Detail Pembayaran:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif;">
                                    <li>Siswa: <?php echo e($payment->user->name); ?></li>
                                    <li>Kursus: <?php echo e($payment->course->title); ?></li>
                                    <li>Jumlah: Rp <?php echo e(number_format($payment->amount, 0, ',', '.')); ?></li>
                                    <li>Metode: <?php echo e($payment->payment_method); ?></li>
                                </ul>
                            </div>
                            <div class="mb-3">
                                <label for="reject_notes<?php echo e($payment->id); ?>" class="form-label" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-weight: 500;">
                                    Alasan Penolakan <span style="color: #3674B5;">*</span>
                                </label>
                                <textarea class="form-control" id="reject_notes<?php echo e($payment->id); ?>" name="notes" rows="3"
                                          placeholder="Jelaskan alasan penolakan pembayaran..." required
                                          style="border-radius: 8px; border: 1px solid #ddd; font-family: 'Poppins', sans-serif;"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                                    style="border-radius: 8px; font-family: 'Poppins', sans-serif;">Batal</button>
                            <button type="submit" class="btn"
                                    style="background: #3674B5; border: none; color: white; border-radius: 8px; font-family: 'Poppins', sans-serif;">
                                <i class="fas fa-times me-1"></i>Tolak Pembayaran
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/admin/payments/index.blade.php ENDPATH**/ ?>