<?php $__env->startSection('title', 'Detail Materi - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #FF3F33; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-file-alt me-2"></i>Detail Materi
                    </h2>
                    <p class="text-muted mb-0"><?php echo e($material->course->title); ?></p>
                </div>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('teacher.materials.edit', $material)); ?>" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit
                    </a>
                    <a href="<?php echo e(route('teacher.materials.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Material Details -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div class="card-header" style="background: linear-gradient(135deg, #075B5E 0%, #9FC87E 100%); border-radius: 15px 15px 0 0; border: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1 text-white" style="font-family: 'Poppins', sans-serif; font-weight: 600;">
                                <?php echo e($material->title); ?>

                            </h5>
                            <small class="text-white opacity-75">
                                <i class="fas fa-sort-numeric-up me-1"></i>Urutan: <?php echo e($material->order); ?>

                            </small>
                        </div>
                        <div class="text-white">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo e($material->created_at->format('d M Y')); ?>

                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <!-- Course Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 rounded-3" style="background: #FFE6E1;">
                                <div class="me-3">
                                    <i class="fas fa-book fa-2x" style="color: #FF3F33;"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1" style="color: #075B5E;">Kursus</h6>
                                    <p class="mb-0 fw-semibold"><?php echo e($material->course->title); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center p-3 rounded-3" style="background: #f8f9fa;">
                                <div class="me-3">
                                    <i class="fas fa-clock fa-2x text-muted"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1" style="color: #075B5E;">Terakhir Diupdate</h6>
                                    <p class="mb-0 fw-semibold"><?php echo e($material->updated_at->format('d M Y, H:i')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="mb-4">
                        <h6 class="mb-3" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-weight: 600;">
                            <i class="fas fa-file-text me-2"></i>Konten Materi
                        </h6>
                        <div class="p-4 rounded-3" style="background: #f8f9fa; border-left: 4px solid #9FC87E;">
                            <div class="content-display">
                                <?php echo nl2br(e($material->content)); ?>

                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="d-flex justify-content-end gap-2">
                        <form action="<?php echo e(route('teacher.materials.destroy', $material)); ?>" 
                              method="POST" class="d-inline delete-form">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="button" class="btn btn-outline-danger delete-btn" 
                                    data-material-title="<?php echo e($material->title); ?>">
                                <i class="fas fa-trash me-1"></i>Hapus Materi
                            </button>
                        </form>
                        <a href="<?php echo e(route('teacher.materials.edit', $material)); ?>" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>Edit Materi
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.content-display {
    line-height: 1.6;
    font-size: 1rem;
    color: #333;
}

.content-display a {
    color: #FF3F33;
    text-decoration: none;
}

.content-display a:hover {
    text-decoration: underline;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fff3cd 100%);
    border: none;
    border-radius: 8px;
    color: #000;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #f7e8a4 100%);
    transform: translateY(-1px);
    color: #000;
}

.btn-outline-danger {
    border-radius: 8px;
}

.card {
    transition: all 0.3s ease;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-link URLs in content
    const contentDisplay = document.querySelector('.content-display');
    if (contentDisplay) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        contentDisplay.innerHTML = contentDisplay.innerHTML.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    }
    
    // Delete confirmation
    document.querySelector('.delete-btn')?.addEventListener('click', function(e) {
        e.preventDefault();
        
        const materialTitle = this.getAttribute('data-material-title');
        const form = this.closest('.delete-form');
        
        Swal.fire({
            title: 'Hapus Materi?',
            text: `Apakah Anda yakin ingin menghapus materi "${materialTitle}"?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#FF3F33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal',
            customClass: {
                popup: 'rounded-3',
                confirmButton: 'rounded-2',
                cancelButton: 'rounded-2'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views\teacher\materials\show.blade.php ENDPATH**/ ?>