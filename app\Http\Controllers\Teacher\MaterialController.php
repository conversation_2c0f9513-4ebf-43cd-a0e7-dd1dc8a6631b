<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Material;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MaterialController extends Controller
{
    /**
     * Display a listing of materials for teacher's courses.
     */
    public function index()
    {
        $courses = Course::where('instructor_id', Auth::id())
            ->with(['materials' => function($query) {
                $query->orderBy('order');
            }])
            ->orderBy('title')
            ->get();

        return view('teacher.materials.index', compact('courses'));
    }

    /**
     * Show the form for creating a new material.
     */
    public function create(Request $request)
    {
        $courseId = $request->get('course_id');
        $courses = Course::where('instructor_id', Auth::id())->orderBy('title')->get();
        
        $selectedCourse = null;
        if ($courseId) {
            $selectedCourse = Course::where('instructor_id', Auth::id())
                ->where('id', $courseId)
                ->first();
        }

        return view('teacher.materials.create', compact('courses', 'selectedCourse'));
    }

    /**
     * Store a newly created material in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'order' => 'required|integer|min:1',
        ]);

        // Verify that the course belongs to the authenticated teacher
        $course = Course::where('id', $request->course_id)
            ->where('instructor_id', Auth::id())
            ->firstOrFail();

        // Check if order already exists and adjust if necessary
        $existingMaterial = Material::where('course_id', $request->course_id)
            ->where('order', $request->order)
            ->first();

        if ($existingMaterial) {
            // Shift existing materials with same or higher order
            Material::where('course_id', $request->course_id)
                ->where('order', '>=', $request->order)
                ->increment('order');
        }

        Material::create([
            'course_id' => $request->course_id,
            'title' => $request->title,
            'content' => $request->content,
            'order' => $request->order,
        ]);

        return redirect()->route('teacher.materials.index')
            ->with('success', 'Materi berhasil ditambahkan!');
    }

    /**
     * Display the specified material.
     */
    public function show(Material $material)
    {
        // Verify that the material belongs to teacher's course
        if ($material->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        return view('teacher.materials.show', compact('material'));
    }

    /**
     * Show the form for editing the specified material.
     */
    public function edit(Material $material)
    {
        // Verify that the material belongs to teacher's course
        if ($material->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $courses = Course::where('instructor_id', Auth::id())->orderBy('title')->get();

        return view('teacher.materials.edit', compact('material', 'courses'));
    }

    /**
     * Update the specified material in storage.
     */
    public function update(Request $request, Material $material)
    {
        // Verify that the material belongs to teacher's course
        if ($material->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'order' => 'required|integer|min:1',
        ]);

        // Verify that the new course belongs to the authenticated teacher
        $course = Course::where('id', $request->course_id)
            ->where('instructor_id', Auth::id())
            ->firstOrFail();

        // Handle order changes
        if ($material->order != $request->order || $material->course_id != $request->course_id) {
            // Remove from old position
            Material::where('course_id', $material->course_id)
                ->where('order', '>', $material->order)
                ->decrement('order');

            // Check if new order already exists in target course
            $existingMaterial = Material::where('course_id', $request->course_id)
                ->where('order', $request->order)
                ->where('id', '!=', $material->id)
                ->first();

            if ($existingMaterial) {
                // Shift existing materials in new position
                Material::where('course_id', $request->course_id)
                    ->where('order', '>=', $request->order)
                    ->increment('order');
            }
        }

        $material->update([
            'course_id' => $request->course_id,
            'title' => $request->title,
            'content' => $request->content,
            'order' => $request->order,
        ]);

        return redirect()->route('teacher.materials.index')
            ->with('success', 'Materi berhasil diperbarui!');
    }

    /**
     * Remove the specified material from storage.
     */
    public function destroy(Material $material)
    {
        // Verify that the material belongs to teacher's course
        if ($material->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $courseId = $material->course_id;
        $order = $material->order;

        $material->delete();

        // Adjust order of remaining materials
        Material::where('course_id', $courseId)
            ->where('order', '>', $order)
            ->decrement('order');

        return redirect()->route('teacher.materials.index')
            ->with('success', 'Materi berhasil dihapus!');
    }
}
