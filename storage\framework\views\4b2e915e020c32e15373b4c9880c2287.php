<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-600 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.75rem;">
                <i class="fas fa-users me-2"></i>Kelola Pengguna
            </h1>
            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Manajemen semua pengguna sistem</p>
        </div>
        <div>
            <button class="btn fw-500" data-bs-toggle="modal" data-bs-target="#createUserModal"
                    style="background: #FF3F33; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                <i class="fas fa-plus me-1"></i>Tambah Pengguna
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #FFE6E1 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #FF3F33;">
                            <i class="fas fa-user-shield fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #FF3F33; font-family: 'Poppins', sans-serif;">
                        <?php echo e($users->where('role.name', 'Admin')->count()); ?>

                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Admin</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #E8F5E8 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #9FC87E;">
                            <i class="fas fa-chalkboard-teacher fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #9FC87E; font-family: 'Poppins', sans-serif;">
                        <?php echo e($users->where('role.name', 'Teacher')->count()); ?>

                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Pengajar</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #E0F2F1 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #075B5E;">
                            <i class="fas fa-user-graduate fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                        <?php echo e($users->where('role.name', 'Student')->count()); ?>

                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Siswa</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card border-0 h-100" style="border-radius: 16px; background: linear-gradient(135deg, #fff 0%, #F3E5F5 100%);">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                             style="width: 60px; height: 60px; background: #6C5CE7;">
                            <i class="fas fa-users fa-lg text-white"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1" style="color: #6C5CE7; font-family: 'Poppins', sans-serif;">
                        <?php echo e($users->count()); ?>

                    </h2>
                    <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card border-0 mb-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
        <div class="card-body p-4">
            <form method="GET" action="<?php echo e(route('admin.users.index')); ?>">
                <div class="row align-items-end">
                    <div class="col-md-4 mb-3">
                        <label for="role" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Role</label>
                        <select class="form-select" id="role" name="role" style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                            <option value="">Semua Role</option>
                            <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($role->name); ?>" <?php echo e(request('role') == $role->name ? 'selected' : ''); ?>>
                                    <?php echo e($role->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="search" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Cari</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo e(request('search')); ?>" placeholder="Nama atau email..."
                               style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                    </div>
                    <div class="col-md-4 mb-3">
                        <button type="submit" class="btn fw-500 me-2"
                                style="background: #FF3F33; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn fw-500"
                           style="background: #FFE6E1; color: #FF3F33; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: 2px solid #FF3F33;">
                            <i class="fas fa-undo me-1"></i>Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
        <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
            <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                <i class="fas fa-list me-2"></i>Daftar Pengguna
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" style="font-family: 'Poppins', sans-serif;">
                    <thead style="background: #F8FAFC;">
                        <tr>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Pengguna</th>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Role</th>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Bergabung</th>
                            <th class="border-0 py-3 px-4" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Status</th>
                            <th class="border-0 py-3 px-4 text-center" style="color: #075B5E; font-weight: 600; font-size: 0.85rem;">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr style="border-bottom: 1px solid #F1F5F9;">
                                <td class="align-middle py-3 px-4">
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                             style="width: 45px; height: 45px; background: linear-gradient(135deg, #FF3F33, #FFE6E1); color: white; font-weight: 600; font-size: 0.9rem;">
                                            <?php echo e(strtoupper(substr($user->name, 0, 1))); ?>

                                        </div>
                                        <div>
                                            <h6 class="mb-1 fw-600" style="color: #075B5E; font-size: 0.9rem;"><?php echo e($user->name); ?></h6>
                                            <small class="text-muted" style="font-size: 0.8rem;"><?php echo e($user->email); ?></small>
                                            <?php if($user->phone): ?>
                                                <br><small class="text-muted" style="font-size: 0.8rem;"><?php echo e($user->phone); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="align-middle py-3 px-4">
                                    <?php if($user->role): ?>
                                        <?php if($user->role->name == 'Admin'): ?>
                                            <span class="badge fw-500" style="background: #FF3F33; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                                <i class="fas fa-user-shield me-1"></i><?php echo e($user->role->name); ?>

                                            </span>
                                        <?php elseif($user->role->name == 'Teacher'): ?>
                                            <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                                <i class="fas fa-chalkboard-teacher me-1"></i><?php echo e($user->role->name); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="badge fw-500" style="background: #075B5E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                                <i class="fas fa-user-graduate me-1"></i><?php echo e($user->role->name); ?>

                                            </span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">No Role</span>
                                    <?php endif; ?>
                                </td>
                                <td class="align-middle py-3 px-4">
                                    <div style="font-size: 0.9rem; color: #075B5E; font-weight: 500;">
                                        <?php echo e($user->created_at->format('d M Y')); ?>

                                    </div>
                                    <small class="text-muted" style="font-size: 0.8rem;"><?php echo e($user->created_at->format('H:i')); ?></small>
                                </td>
                                <td class="align-middle py-3 px-4">
                                    <?php if($user->isActive()): ?>
                                        <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                            <i class="fas fa-check me-1"></i>Aktif
                                        </span>
                                    <?php else: ?>
                                        <span class="badge fw-500" style="background: #FF3F33; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                            <i class="fas fa-times me-1"></i>Nonaktif
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="align-middle text-center py-3 px-4">
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.users.show', $user)); ?>" class="btn btn-sm me-1"
                                           title="Lihat Detail"
                                           style="background: #075B5E; color: white; border-radius: 8px; padding: 0.4rem 0.6rem; border: none;">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if($user->id !== auth()->id() && !($user->isAdmin() && auth()->user()->isAdmin())): ?>
                                            <button class="btn btn-sm me-1"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#editRoleModal<?php echo e($user->id); ?>"
                                                    title="Edit Role"
                                                    style="background: #E0F2F1; color: #075B5E; border-radius: 8px; padding: 0.4rem 0.6rem; border: 1px solid #075B5E;">
                                                <i class="fas fa-user-edit"></i>
                                            </button>
                                            <button class="btn btn-sm"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#toggleStatusModal<?php echo e($user->id); ?>"
                                                    title="<?php echo e($user->isActive() ? 'Nonaktifkan' : 'Aktifkan'); ?>"
                                                    style="background: <?php echo e($user->isActive() ? '#FFE6E1' : '#E8F5E8'); ?>; color: <?php echo e($user->isActive() ? '#FF3F33' : '#9FC87E'); ?>; border-radius: 8px; padding: 0.4rem 0.6rem; border: 1px solid <?php echo e($user->isActive() ? '#FF3F33' : '#9FC87E'); ?>;">
                                                <i class="fas fa-<?php echo e($user->isActive() ? 'user-times' : 'user-check'); ?>"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center mb-3"
                                             style="width: 80px; height: 80px; background: #F8FAFC; color: #9CA3AF;">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                        <h5 class="text-muted fw-500" style="font-family: 'Poppins', sans-serif;">Tidak Ada Pengguna</h5>
                                        <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada pengguna yang terdaftar</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <?php if($users->hasPages()): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    <?php echo e($users->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Modals for status toggle -->
<?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if($user->id !== auth()->id() && !($user->isAdmin() && auth()->user()->isAdmin())): ?>
        <div class="modal fade" id="toggleStatusModal<?php echo e($user->id); ?>" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-<?php echo e($user->isActive() ? 'user-times' : 'user-check'); ?> me-2" style="color: <?php echo e($user->isActive() ? '#FF3F33' : '#9FC87E'); ?>;"></i>
                            <?php echo e($user->isActive() ? 'Nonaktifkan' : 'Aktifkan'); ?> Pengguna
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="<?php echo e(route('admin.users.toggle-status', $user)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert border-0" style="background: <?php echo e($user->isActive() ? '#FFE6E1' : '#E8F5E8'); ?>; color: <?php echo e($user->isActive() ? '#7F1D1D' : '#2D5016'); ?>; border-radius: 12px;">
                                <h6 class="fw-600" style="font-family: 'Poppins', sans-serif;">Detail Pengguna:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <li>Nama: <?php echo e($user->name); ?></li>
                                    <li>Email: <?php echo e($user->email); ?></li>
                                    <li>Role: <?php echo e($user->role->name ?? 'No Role'); ?></li>
                                    <li>Status: <?php echo e($user->isActive() ? 'Aktif' : 'Nonaktif'); ?></li>
                                </ul>
                            </div>
                            <p class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                Apakah Anda yakin ingin <?php echo e($user->isActive() ? 'menonaktifkan' : 'mengaktifkan'); ?> pengguna ini?
                            </p>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn fw-500" data-bs-dismiss="modal"
                                    style="background: #F8FAFC; color: #6B7280; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                Batal
                            </button>
                            <button type="submit" class="btn fw-500"
                                    style="background: <?php echo e($user->isActive() ? '#FF3F33' : '#9FC87E'); ?>; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                <i class="fas fa-<?php echo e($user->isActive() ? 'user-times' : 'user-check'); ?> me-1"></i>
                                <?php echo e($user->isActive() ? 'Nonaktifkan' : 'Aktifkan'); ?>

                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- Edit Role Modals -->
<?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php if($user->id !== auth()->id() && !($user->isAdmin() && auth()->user()->isAdmin())): ?>
        <div class="modal fade" id="editRoleModal<?php echo e($user->id); ?>" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content" style="border-radius: 16px; border: none;">
                    <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                        <h5 class="modal-title fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                            <i class="fas fa-user-edit me-2" style="color: #075B5E;"></i>Edit Role Pengguna
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="<?php echo e(route('admin.users.update-role', $user)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <div class="modal-body" style="padding: 0 1.5rem;">
                            <div class="alert border-0" style="background: #E0F2F1; color: #2D5016; border-radius: 12px;">
                                <h6 class="fw-600" style="font-family: 'Poppins', sans-serif;">Detail Pengguna:</h6>
                                <ul class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <li>Nama: <?php echo e($user->name); ?></li>
                                    <li>Email: <?php echo e($user->email); ?></li>
                                    <li>Role Saat Ini: <?php echo e($user->role->name ?? 'No Role'); ?></li>
                                </ul>
                            </div>
                            <div class="mb-3">
                                <label for="role_id_<?php echo e($user->id); ?>" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Role Baru</label>
                                <select class="form-select" id="role_id_<?php echo e($user->id); ?>" name="role_id" required
                                        style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($role->id); ?>" <?php echo e($user->role_id == $role->id ? 'selected' : ''); ?>>
                                            <?php echo e($role->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                            <button type="button" class="btn fw-500" data-bs-dismiss="modal"
                                    style="background: #F8FAFC; color: #6B7280; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                Batal
                            </button>
                            <button type="submit" class="btn fw-500"
                                    style="background: #075B5E; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                                <i class="fas fa-save me-1"></i>Simpan Perubahan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 16px; border: none;">
            <div class="modal-header border-0" style="padding: 1.5rem 1.5rem 0;">
                <h5 class="modal-title fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                    <i class="fas fa-user-plus me-2" style="color: #FF3F33;"></i>Tambah Pengguna Baru
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="<?php echo e(route('admin.users.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body" style="padding: 0 1.5rem;">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Nama Lengkap</label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required
                                   style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Nomor Telepon</label>
                            <input type="text" class="form-control" id="phone" name="phone" required
                                   style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="role_id" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Role</label>
                            <select class="form-select" id="role_id" name="role_id" required
                                    style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                <option value="">Pilih Role</option>
                                <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($role->id); ?>"><?php echo e($role->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="8"
                                   style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="password_confirmation" class="form-label fw-500" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Konfirmasi Password</label>
                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required minlength="8"
                                   style="border-radius: 12px; border: 2px solid #E5E7EB; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0" style="padding: 0 1.5rem 1.5rem;">
                    <button type="button" class="btn fw-500" data-bs-dismiss="modal"
                            style="background: #F8FAFC; color: #6B7280; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                        Batal
                    </button>
                    <button type="submit" class="btn fw-500"
                            style="background: #FF3F33; color: white; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
                        <i class="fas fa-user-plus me-1"></i>Tambah Pengguna
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views/admin/users/index.blade.php ENDPATH**/ ?>