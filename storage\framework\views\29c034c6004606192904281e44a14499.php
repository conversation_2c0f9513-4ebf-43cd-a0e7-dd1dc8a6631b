

<?php $__env->startSection('title', 'Daftar Kursus - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-5 fw-bold text-center text-primary mb-2">
            <i class="fas fa-book-open me-3"></i>Daftar Kursus
        </h1>
        <p class="text-center text-muted lead">Temukan kursus terbaik untuk mengembangkan kemampuan Anda</p>
    </div>
</div>

<?php if(session('success')): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if(session('error')): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if($courses->isEmpty()): ?>
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-book-open text-muted" style="font-size: 4rem;"></i>
    </div>
    <h3 class="text-muted">Belum Ada Kursus</h3>
    <p class="text-muted">Kursus akan segera tersedia. Silakan kembali lagi nanti.</p>
</div>
<?php else: ?>
<div class="row g-4">
    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm border-0 card-hover">
            <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <h5 class="card-title mb-0 text-truncate">
                    <i class="fas fa-graduation-cap me-2"></i><?php echo e($course->title); ?>

                </h5>
            </div>
            
            <div class="card-body d-flex flex-column">
                <div class="mb-3">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-user-tie me-1"></i>Pengajar
                    </h6>
                    <p class="fw-semibold text-dark"><?php echo e($course->instructor_name); ?></p>
                </div>
                
                <div class="mb-3 flex-grow-1">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-info-circle me-1"></i>Deskripsi
                    </h6>
                    <p class="text-dark"><?php echo e(Str::limit($course->description, 100)); ?></p>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-muted mb-1">
                        <i class="fas fa-tag me-1"></i>Harga
                    </h6>
                    <h4 class="text-primary fw-bold"><?php echo e($course->formatted_price); ?></h4> 
                    
                </div>
             

                <!-- Status Pendaftaran -->
                <?php if(auth()->guard()->check()): ?>
                    <?php
                        $isRegistered = $course->students()->where('user_id', auth()->id())->exists();
                    ?>
                    
                    <?php if($isRegistered): ?>
                    <div class="mb-3">
                        <div class="alert alert-success py-2 mb-0">
                            <i class="fas fa-check-circle me-2"></i>Anda sudah terdaftar
                        </div>
                    </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <div class="card-footer bg-transparent border-0 pt-0">
                <?php if(auth()->guard()->guest()): ?>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-primary btn-lg w-100 fw-semibold">
                        <i class="fas fa-sign-in-alt me-2"></i>Login untuk Mendaftar
                    </a>
                <?php else: ?>
                    <?php
                        $isRegistered = $course->students()->where('user_id', auth()->id())->exists();
                    ?>
                    
                    <?php if($isRegistered): ?>
                        <a href="<?php echo e(route('courses.show', $course)); ?>" class="btn btn-success btn-lg w-100 fw-semibold">
                            <i class="fas fa-eye me-2"></i>Lihat Kursus
                        </a>
                    <?php else: ?>
                        <form action="<?php echo e(route('courses.register', $course)); ?>" method="POST" class="d-inline w-100">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-primary btn-lg w-100 fw-semibold" 
                                    onclick="return confirm('Apakah Anda yakin ingin mendaftar kursus ini?')">
                                <i class="fas fa-user-plus me-2"></i>Daftar Sekarang
                            </button>
                        </form>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<!-- Pagination -->
<?php if($courses instanceof \Illuminate\Pagination\LengthAwarePaginator): ?>
<div class="d-flex justify-content-center mt-4">
    <?php echo e($courses->links()); ?>

</div>
<?php endif; ?>

<!-- Statistics -->
<div class="mt-5 py-4 bg-primary rounded text-white text-center">
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <i class="fas fa-book text-white-50" style="font-size: 2rem;"></i>
            </div>
            <h3 class="fw-bold"><?php echo e($courses->count()); ?></h3>
            <p class="mb-0">Kursus Tersedia</p>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <i class="fas fa-users text-white-50" style="font-size: 2rem;"></i>
            </div>
            <h3 class="fw-bold"><?php echo e(\App\Models\User::whereHas('studentCourses')->count()); ?>+</h3>
            <p class="mb-0">Siswa Terdaftar</p>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <i class="fas fa-star text-white-50" style="font-size: 2rem;"></i>
            </div>
            <h3 class="fw-bold">4.9</h3>
            <p class="mb-0">Rating Rata-rata</p>
        </div>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views\courses\student.blade.php ENDPATH**/ ?>