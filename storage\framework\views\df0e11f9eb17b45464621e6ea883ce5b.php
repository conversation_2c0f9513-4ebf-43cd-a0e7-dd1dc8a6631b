<?php $__env->startSection('title', 'Login - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-vh-100 d-flex align-items-center" style="background: linear-gradient(135deg, var(--light-red) 0%, #fff 50%, var(--light-green) 100%);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="card border-0 shadow-lg" style="border-radius: 24px; overflow: hidden;">
                    <!-- Header with gradient -->
                    <div class="card-header text-center py-4" style="background: linear-gradient(135deg, var(--dark-teal) 0%, var(--primary-red) 100%); border: none;">
                        <div class="mb-3">
                            <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-white" style="width: 80px; height: 80px;">
                                <i class="fas fa-graduation-cap fa-2x" style="color: var(--primary-red);"></i>
                            </div>
                        </div>
                        <h2 class="text-white fw-bold mb-2">Selamat Datang</h2>
                        <p class="text-white-50 mb-0">Masuk ke akun Sistem Kursus Anda</p>
                    </div>

                    <div class="card-body p-4">
                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger border-0 rounded-3 mb-4" style="background: var(--light-red);">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-circle me-2" style="color: var(--primary-red);"></i>
                                    <div>
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div><?php echo e($error); ?></div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <form action="<?php echo e(route('login')); ?>" method="POST">
                            <?php echo csrf_field(); ?>

                            <div class="mb-4">
                                <label for="email" class="form-label fw-semibold mb-2" style="color: var(--dark-teal);">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                <input type="email"
                                       class="form-control form-control-lg <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       id="email"
                                       name="email"
                                       value="<?php echo e(old('email')); ?>"
                                       placeholder="Masukkan email Anda"
                                       style="border-radius: 12px; border: 2px solid #e9ecef; padding: 0.875rem 1rem;"
                                       required>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label fw-semibold mb-2" style="color: var(--dark-teal);">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <div class="position-relative">
                                    <input type="password"
                                           class="form-control form-control-lg <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="password"
                                           name="password"
                                           placeholder="Masukkan password Anda"
                                           style="border-radius: 12px; border: 2px solid #e9ecef; padding: 0.875rem 1rem;"
                                           required>
                                    <button type="button" class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                            onclick="togglePassword()" style="border: none; background: none;">
                                        <i class="fas fa-eye" id="toggleIcon" style="color: var(--dark-teal);"></i>
                                    </button>
                                </div>
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember" name="remember"
                                           style="border-radius: 6px;">
                                    <label class="form-check-label text-muted" for="remember">
                                        Ingat saya
                                    </label>
                                </div>
                                <a href="#" class="text-decoration-none" style="color: var(--primary-red); font-weight: 500;">
                                    Lupa password?
                                </a>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-lg fw-semibold"
                                        style="background: linear-gradient(135deg, var(--primary-red) 0%, #e63946 100%);
                                               border: none; border-radius: 12px; padding: 1rem; color: white;
                                               transition: all 0.3s ease;">
                                    <i class="fas fa-sign-in-alt me-2"></i>Masuk ke Akun
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="text-muted mb-0">
                                Belum punya akun?
                                <a href="<?php echo e(route('register')); ?>" class="text-decoration-none fw-semibold"
                                   style="color: var(--primary-red);">
                                    Daftar sekarang
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Add focus effects
document.querySelectorAll('.form-control').forEach(input => {
    input.addEventListener('focus', function() {
        this.style.borderColor = 'var(--primary-red)';
        this.style.boxShadow = '0 0 0 0.2rem rgba(255, 63, 51, 0.25)';
    });

    input.addEventListener('blur', function() {
        this.style.borderColor = '#e9ecef';
        this.style.boxShadow = 'none';
    });
});

// Button hover effect
document.querySelector('button[type="submit"]').addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-2px)';
    this.style.boxShadow = '0 8px 25px rgba(255, 63, 51, 0.3)';
});

document.querySelector('button[type="submit"]').addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0)';
    this.style.boxShadow = 'none';
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views\auth\login.blade.php ENDPATH**/ ?>