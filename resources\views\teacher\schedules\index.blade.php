@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - Sistem Kursus')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-calendar me-2"></i><PERSON><PERSON><PERSON>wal
                    </h2>
                    <p class="text-muted mb-0"><PERSON><PERSON><PERSON> jadwal pembelajaran untuk kursus Anda</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('teacher.schedules.calendar') }}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar-alt me-2"></i><PERSON><PERSON><PERSON>
                    </a>
                    <a href="{{ route('teacher.schedules.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><PERSON><PERSON> Jadwal
                    </a>
                </div>
            </div>
        </div>
    </div>

    {{-- <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif --}}

    <!-- Filters -->
    <div class="card mb-4" style="border-radius: 15px; border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <div class="card-body">
            <form method="GET" action="{{ route('teacher.schedules.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="course_id" class="form-label small fw-semibold">Kursus</label>
                        <select class="form-select form-select-sm" id="course_id" name="course_id">
                            <option value="">Semua Kursus</option>
                            @foreach($courses as $course)
                                <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                    {{ $course->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label small fw-semibold">Tipe</label>
                        <select class="form-select form-select-sm" id="type" name="type">
                            <option value="">Semua Tipe</option>
                            <option value="online" {{ request('type') == 'online' ? 'selected' : '' }}>Online</option>
                            <option value="offline" {{ request('type') == 'offline' ? 'selected' : '' }}>Offline</option>
                            <option value="hybrid" {{ request('type') == 'hybrid' ? 'selected' : '' }}>Hybrid</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="start_date" class="form-label small fw-semibold">Dari Tanggal</label>
                        <input type="date" class="form-control form-control-sm" id="start_date" name="start_date" value="{{ request('start_date') }}">
                    </div>
                    <div class="col-md-2">
                        <label for="end_date" class="form-label small fw-semibold">Sampai Tanggal</label>
                        <input type="date" class="form-control form-control-sm" id="end_date" name="end_date" value="{{ request('end_date') }}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2 w-100">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="{{ route('teacher.schedules.index') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-undo me-1"></i>Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Schedules List -->
    <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <div class="card-body p-0">
            @forelse($schedules as $schedule)
                <div class="d-flex align-items-center p-4 border-bottom schedule-item">
                    <div class="me-3">
                        <div class="rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 50px; height: 50px; background: {{ $schedule->type == 'online' ? '#e3f2fd' : ($schedule->type == 'offline' ? '#f3e5f5' : '#e8f5e8') }};">
                            <i class="fas {{ $schedule->type == 'online' ? 'fa-video' : ($schedule->type == 'offline' ? 'fa-map-marker-alt' : 'fa-globe') }}"
                               style="color: {{ $schedule->type == 'online' ? '#1976d2' : ($schedule->type == 'offline' ? '#7b1fa2' : '#388e3c') }};"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                {{ $schedule->title }}
                            </h6>
                            <span class="badge {{ $schedule->type == 'online' ? 'bg-primary' : ($schedule->type == 'offline' ? 'bg-purple' : 'bg-success') }}">
                                {{ ucfirst($schedule->type) }}
                            </span>
                        </div>
                        <p class="text-muted mb-2 small">{{ $schedule->course->title }}</p>
                        <div class="d-flex align-items-center text-muted small">
                            <i class="fas fa-calendar me-1"></i>
                            {{ $schedule->date->format('d M Y') }}
                            <span class="mx-2">•</span>
                            <i class="fas fa-clock me-1"></i>
                            {{ $schedule->start_time->format('H:i') }} - {{ $schedule->end_time->format('H:i') }}
                            @if($schedule->location)
                                <span class="mx-2">•</span>
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ $schedule->location }}
                            @endif
                        </div>
                    </div>
                    <div class="ms-3">
                        <div class="btn-group" role="group">
                            {{-- <a href="{{ route('teacher.schedules.show', $schedule) }}" 
                               class="btn btn-outline-info btn-sm" title="Lihat">
                                <i class="fas fa-eye"></i>
                            </a> --}}
                            <a href="{{ route('teacher.schedules.edit', $schedule) }}" 
                               class="btn btn-outline-warning btn-sm" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form action="{{ route('teacher.schedules.destroy', $schedule) }}" 
                                  method="POST" class="d-inline delete-form">
                                @csrf
                                @method('DELETE')
                                <button type="button" class="btn btn-outline-danger btn-sm delete-btn" 
                                        data-schedule-title="{{ $schedule->title }}" title="Hapus">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-calendar-times fa-3x text-muted"></i>
                    </div>
                    <h6 class="text-muted">Belum ada jadwal</h6>
                    <p class="text-muted small mb-3">Mulai tambahkan jadwal pembelajaran untuk kursus Anda</p>
                    <a href="{{ route('teacher.schedules.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Tambah Jadwal Pertama
                    </a>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Pagination -->
    @if($schedules->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $schedules->links() }}
        </div>
    @endif
</div>

<style>
.schedule-item:hover {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px;
}

.bg-purple {
    background-color: #7b1fa2 !important;
}

.alert {
    border-radius: 12px;
    border: none;
}

.card {
    transition: all 0.3s ease;
}
</style>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete confirmation
    document.querySelectorAll('.delete-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const scheduleTitle = this.getAttribute('data-schedule-title');
            const form = this.closest('.delete-form');
            
            Swal.fire({
                title: 'Hapus Jadwal?',
                text: `Apakah Anda yakin ingin menghapus jadwal "${scheduleTitle}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3674B5',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal',
                customClass: {
                    popup: 'rounded-3',
                    confirmButton: 'rounded-2',
                    cancelButton: 'rounded-2'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });
});
</script>
@endpush
