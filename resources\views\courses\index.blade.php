@extends('layouts.app')

@section('title', 'Daftar Kursus - Sistem Kursus')

@section('content')
<!-- Header Section -->
<div class="row py-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm" style="background: #3674B5; border-radius: 16px;">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <h1 class="text-white fw-600 mb-2" style="font-size: 1.75rem; font-family: 'Poppins', sans-serif;">
                                Jelajahi Kursus Terbaik
                            </h1>
                            <p class="text-white opacity-75 mb-0" style="font-size: 0.95rem; font-family: 'Poppins', sans-serif;">
                                Temukan kursus yang tepat untuk mengembangkan skill dan meraih impian karir <PERSON>
                            </p>
                        </div>

                        <!-- Search Bar -->
                        {{-- <div class="position-relative" style="max-width: 400px;">
                            <input type="text"
                                   class="form-control pe-5"
                                   placeholder="Cari kursus, topik, atau pengajar..."
                                   id="searchCourse"
                                   style="border-radius: 12px; border: none; padding: 0.75rem 1rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                            <button class="btn position-absolute end-0 top-50 translate-middle-y me-2"
                                    type="button"
                                    style="background: #075B5E; color: white; border-radius: 8px; width: 36px; height: 36px; border: none;">
                                <i class="fas fa-search" style="font-size: 0.8rem;"></i>
                            </button>
                        </div> --}}
                        <a href="{{ route('login') }}" class="btn fw-500"
                               style="background: white; color: #3674B5; border-radius: 8px; text-decoration: none; font-family: 'Poppins', sans-serif; font-size: 0.85rem; padding: 0.6rem;">
                                <i class="fas fa-sign-in-alt me-1" style="font-size: 0.8rem;"></i>Login
                            </a>
                    </div>
                    <div class="col-md-4 text-end d-none d-md-block">
                        <div class="position-relative">
                            <i class="fas fa-graduation-cap text-white" style="font-size: 4rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Courses Grid -->
<div class="row">
    @forelse ($courses as $course)
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card h-100 border-0 course-card shadow-sm" style="border-radius: 12px; overflow: hidden; transition: all 0.3s ease;">
                <!-- Course Image Placeholder -->
                <div class="position-relative" style="height: 180px; background: #F5F0CD;">
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge fw-500" style="background: #3674B5; color: white; border-radius: 8px; padding: 0.4rem 0.8rem; font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                            {{ $course->formatted_price }}
                        </span>
                    </div>
                    <div class="position-absolute bottom-0 start-0 m-3">
                        <div class="d-flex align-items-center">
                            @if($course->reviews_count > 0)
                                <div class="bg-white rounded-pill px-2 py-1 d-flex align-items-center shadow-sm">
                                    <i class="fas fa-star text-warning me-1" style="font-size: 0.8rem;"></i>
                                    <span class="fw-500" style="color: #3674B5; font-size: 0.8rem; font-family: 'Poppins', sans-serif;">{{ number_format($course->average_rating, 1) }}</span>
                                    <small class="text-muted ms-1" style="font-size: 0.7rem;">({{ $course->reviews_count }})</small>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <i class="fas fa-play-circle" style="font-size: 2.5rem; color: #3674B5; opacity: 0.6;"></i>
                    </div>
                </div>

                <div class="card-body p-3">
                    <div class="mb-3">
                        <h5 class="card-title fw-600 mb-2" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                            {{ $course->title }}
                        </h5>
                        <p class="card-text text-muted mb-0" style="font-size: 0.85rem; line-height: 1.4; font-family: 'Poppins', sans-serif;">
                            {{ Str::limit($course->description, 100) }}
                        </p>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-light rounded-circle p-2 me-2" style="width: 36px; height: 36px;">
                                <i class="fas fa-user" style="color: #3674B5; font-size: 0.9rem;"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-500" style="font-size: 0.85rem; font-family: 'Poppins', sans-serif;">{{ $course->instructor->name ?? 'Belum ditentukan' }}</h6>
                                <small class="text-muted" style="font-size: 0.75rem;">Pengajar</small>
                            </div>
                        </div>
                    </div>

                    <div class="row text-center mb-2">
                        <div class="col-4">
                            <div class="p-1">
                                <i class="fas fa-users" style="color: #578FCA; font-size: 0.9rem;"></i>
                                <div class="fw-500" style="font-size: 0.8rem; font-family: 'Poppins', sans-serif;">{{ $course->total_students }}</div>
                                <small class="text-muted" style="font-size: 0.7rem;">Siswa</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-1">
                                <i class="fas fa-clock" style="color: #3674B5; font-size: 0.9rem;"></i>
                                <div class="fw-500" style="font-size: 0.8rem; font-family: 'Poppins', sans-serif;">{{ rand(4, 12) }}h</div>
                                <small class="text-muted" style="font-size: 0.7rem;">Durasi</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-1">
                                <i class="fas fa-certificate" style="color: #3674B5; font-size: 0.9rem;"></i>
                                <div class="fw-500" style="font-size: 0.8rem; font-family: 'Poppins', sans-serif;">Ya</div>
                                <small class="text-muted" style="font-size: 0.7rem;">Sertifikat</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer bg-transparent border-0 p-3 pt-0">
                    <div class="d-grid gap-2">
                        <a href="{{ route('courses.show', $course) }}"
                           class="btn"
                           style="border-radius: 8px; border: 1px solid #3674B5; color: #3674B5; font-family: 'Poppins', sans-serif; font-size: 0.85rem; padding: 0.5rem;">
                            <i class="fas fa-eye me-1" style="font-size: 0.8rem;"></i>Lihat Detail
                        </a>
                        @auth
                            @if(auth()->user()->isStudent())
                                @if(!auth()->user()->enrolledCourses->contains($course->id))
                                    <form action="{{ route('student.courses.enroll', $course) }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn fw-500 w-100"
                                                style="background: #3674B5; color: white; border-radius: 8px; border: none; font-family: 'Poppins', sans-serif; font-size: 0.85rem; padding: 0.6rem;">
                                            <i class="fas fa-user-plus me-1" style="font-size: 0.8rem;"></i>Daftar Kursus
                                        </button>
                                    </form>
                                @else
                                    <button class="btn fw-500 w-100" disabled
                                            style="background: #578FCA; color: white; border-radius: 8px; border: none; font-family: 'Poppins', sans-serif; font-size: 0.85rem; padding: 0.6rem;">
                                        <i class="fas fa-check me-1" style="font-size: 0.8rem;"></i>Sudah Terdaftar
                                    </button>
                                @endif
                            @elseif(auth()->user()->isTeacher())
                                <button class="btn fw-500 w-100" disabled
                                        style="background: #F5F0CD; color: #3674B5; border: 1px solid #3674B5; border-radius: 8px; font-family: 'Poppins', sans-serif; font-size: 0.85rem; padding: 0.6rem;">
                                    <i class="fas fa-chalkboard-teacher me-1" style="font-size: 0.8rem;"></i>Anda Pengajar
                                </button>
                            @else
                                <button class="btn fw-500 w-100" disabled
                                        style="background: #F5F0CD; color: #3674B5; border: 1px solid #3674B5; border-radius: 8px; font-family: 'Poppins', sans-serif; font-size: 0.85rem; padding: 0.6rem;">
                                    <i class="fas fa-user-shield me-1" style="font-size: 0.8rem;"></i>Admin
                                </button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="btn fw-500"
                               style="background: #3674B5; color: white; border-radius: 8px; text-decoration: none; font-family: 'Poppins', sans-serif; font-size: 0.85rem; padding: 0.6rem;">
                                <i class="fas fa-sign-in-alt me-1" style="font-size: 0.8rem;"></i>Login untuk Daftar
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-12">
            <div class="text-center py-4">
                <div class="mb-3">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                         style="width: 80px; height: 80px; background: #F5F0CD;">
                        <i class="fas fa-book-open" style="color: #3674B5; font-size: 1.8rem;"></i>
                    </div>
                </div>
                <h4 class="mb-2" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-size: 1.3rem; font-weight: 600;">Belum Ada Kursus</h4>
                <p class="text-muted mb-3" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Kursus akan segera tersedia. Silakan cek kembali nanti atau hubungi admin.</p>
                @auth
                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('admin.courses.create') }}" class="btn fw-500"
                           style="background: #3674B5; color: white; border-radius: 8px; text-decoration: none; font-family: 'Poppins', sans-serif; font-size: 0.9rem; padding: 0.6rem 1.2rem;">
                            <i class="fas fa-plus me-1" style="font-size: 0.8rem;"></i>Tambah Kursus Pertama
                        </a>
                    @endif
                @endauth
            </div>
        </div>
    @endforelse
</div>

@if($courses->count() > 0)
    <!-- Stats Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 12px; background: #f8f9fa;">
                <div class="card-body text-center p-3">
                    <div class="row">
                        <div class="col-md-3">
                            <h4 class="fw-600" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-size: 1.5rem;">{{ $courses->count() }}</h4>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total Kursus</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="fw-600" style="color: #578FCA; font-family: 'Poppins', sans-serif; font-size: 1.5rem;">{{ $courses->sum('total_students') }}</h4>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total Siswa</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="fw-600" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-size: 1.5rem;">{{ $courses->where('reviews_count', '>', 0)->count() }}</h4>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Kursus Tereviewed</p>
                        </div>
                        <div class="col-md-3">
                            <h4 class="fw-600 text-warning" style="font-family: 'Poppins', sans-serif; font-size: 1.5rem;">{{ number_format($courses->avg('average_rating'), 1) }}</h4>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Rating Rata-rata</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

<style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Font weights */
.fw-300 { font-weight: 300; }
.fw-400 { font-weight: 400; }
.fw-500 { font-weight: 500; }
.fw-600 { font-weight: 600; }
.fw-700 { font-weight: 700; }

/* Filter buttons */
.filter-btn {
    background: #f8f9fa;
    color: #3674B5;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #3674B5;
    color: white;
    border-color: #3674B5;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(54, 116, 181, 0.2);
}

/* Course cards */
.course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.course-card:hover .card-title {
    color: #3674B5 !important;
}

/* Search input focus */
#searchCourse:focus {
    box-shadow: 0 0 0 0.2rem rgba(54, 116, 181, 0.15);
    border-color: #3674B5;
    outline: none;
}

/* Button hover effects */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Animation for course cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-card {
    animation: fadeInUp 0.4s ease forwards;
}

.course-card:nth-child(1) { animation-delay: 0.05s; }
.course-card:nth-child(2) { animation-delay: 0.1s; }
.course-card:nth-child(3) { animation-delay: 0.15s; }
.course-card:nth-child(4) { animation-delay: 0.2s; }
.course-card:nth-child(5) { animation-delay: 0.25s; }
.course-card:nth-child(6) { animation-delay: 0.3s; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchCourse');
    const courseCards = document.querySelectorAll('.course-card');

    searchInput.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();

        courseCards.forEach(card => {
            const title = card.querySelector('.card-title').textContent.toLowerCase();
            const description = card.querySelector('.card-text').textContent.toLowerCase();
            const instructor = card.querySelector('.fw-semibold').textContent.toLowerCase();

            if (title.includes(searchTerm) || description.includes(searchTerm) || instructor.includes(searchTerm)) {
                card.parentElement.style.display = 'block';
                card.style.animation = 'fadeInUp 0.3s ease forwards';
            } else {
                card.parentElement.style.display = 'none';
            }
        });

        // Show "no results" message if no cards are visible
        const visibleCards = Array.from(courseCards).filter(card =>
            card.parentElement.style.display !== 'none'
        );

        if (visibleCards.length === 0 && searchTerm.length > 0) {
            showNoResultsMessage();
        } else {
            hideNoResultsMessage();
        }
    });

    // Filter functionality
    const filterButtons = document.querySelectorAll('.filter-btn');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            const filter = this.getAttribute('data-filter');

            courseCards.forEach(card => {
                if (filter === 'all') {
                    card.parentElement.style.display = 'block';
                } else {
                    // This is a placeholder - you would implement actual filtering logic
                    // based on course categories stored in data attributes
                    card.parentElement.style.display = 'block';
                }
            });
        });
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

function showNoResultsMessage() {
    const existingMessage = document.getElementById('noResultsMessage');
    if (existingMessage) return;

    const coursesGrid = document.querySelector('.row:has(.course-card)');
    const noResultsDiv = document.createElement('div');
    noResultsDiv.id = 'noResultsMessage';
    noResultsDiv.className = 'col-12 text-center py-5';
    noResultsDiv.innerHTML = `
        <div class="mb-4">
            <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                 style="width: 100px; height: 100px; background: #F5F0CD;">
                <i class="fas fa-search fa-2x" style="color: #3674B5;"></i>
            </div>
        </div>
        <h5 style="color: #3674B5;">Tidak Ada Hasil</h5>
        <p class="text-muted">Coba gunakan kata kunci yang berbeda atau jelajahi semua kursus.</p>
    `;

    coursesGrid.appendChild(noResultsDiv);
}

function hideNoResultsMessage() {
    const existingMessage = document.getElementById('noResultsMessage');
    if (existingMessage) {
        existingMessage.remove();
    }
}
</script>

@endsection
