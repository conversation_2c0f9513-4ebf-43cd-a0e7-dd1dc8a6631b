<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StudentController extends Controller
{
    /**
     * Display a listing of students enrolled in teacher's courses.
     */
    public function index(Request $request)
    {
        $query = Enrollment::whereHas('course', function($q) {
            $q->where('instructor_id', Auth::id());
        })->with(['user', 'course']);

        // Filter by course if specified
        if ($request->filled('course_id')) {
            $query->where('course_id', $request->course_id);
        }

        // Filter by enrollment status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by student name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $enrollments = $query->orderBy('enrolled_at', 'desc')->paginate(15);

        $courses = Course::where('instructor_id', Auth::id())->orderBy('title')->get();

        // Get statistics
        $stats = [
            'total_students' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->count(),
            'active_students' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->where('status', 'active')->count(),
            'completed_students' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->where('status', 'completed')->count(),
            'cancelled_students' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->where('status', 'cancelled')->count(),
        ];

        return view('teacher.students.index', compact('enrollments', 'courses', 'stats'));
    }

    /**
     * Display the specified student's details.
     */
    public function show(User $student, Request $request)
    {
        // Get student's enrollments in teacher's courses
        $enrollments = Enrollment::whereHas('course', function($q) {
            $q->where('instructor_id', Auth::id());
        })->where('user_id', $student->id)
          ->with(['course', 'course.payments' => function($q) use ($student) {
              $q->where('user_id', $student->id);
          }])
          ->get();

        if ($enrollments->isEmpty()) {
            abort(404, 'Siswa tidak ditemukan dalam kursus Anda.');
        }

        // Get student's reviews for teacher's courses
        $reviews = $student->reviews()
            ->whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })
            ->with('course')
            ->get();

        return view('teacher.students.show', compact('student', 'enrollments', 'reviews'));
    }

    /**
     * Update student's enrollment status.
     */
    public function updateEnrollmentStatus(Request $request, Enrollment $enrollment)
    {
        // Verify that the enrollment belongs to teacher's course
        if ($enrollment->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $request->validate([
            'status' => 'required|in:active,completed,cancelled',
        ]);

        $oldStatus = $enrollment->status;
        $enrollment->update(['status' => $request->status]);

        $statusLabels = [
            'active' => 'Aktif',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
        ];

        return redirect()->back()
            ->with('success', "Status pendaftaran berhasil diubah dari {$statusLabels[$oldStatus]} menjadi {$statusLabels[$request->status]}.");
    }

    /**
     * Show students by course.
     */
    public function byCourse(Course $course)
    {
        // Verify that the course belongs to the authenticated teacher
        if ($course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $enrollments = $course->enrollments()
            ->with('user')
            ->orderBy('enrolled_at', 'desc')
            ->paginate(15);

        $stats = [
            'total_students' => $course->enrollments()->count(),
            'active_students' => $course->enrollments()->where('status', 'active')->count(),
            'completed_students' => $course->enrollments()->where('status', 'completed')->count(),
            'cancelled_students' => $course->enrollments()->where('status', 'cancelled')->count(),
        ];

        return view('teacher.students.by-course', compact('course', 'enrollments', 'stats'));
    }

    /**
     * Export students list for a course.
     */
    public function export(Course $course)
    {
        // Verify that the course belongs to the authenticated teacher
        if ($course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $enrollments = $course->enrollments()
            ->with('user')
            ->orderBy('enrolled_at', 'desc')
            ->get();

        $filename = 'siswa_' . str_replace(' ', '_', strtolower($course->title)) . '_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($enrollments) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Header
            fputcsv($file, ['Nama', 'Email', 'Telepon', 'Status', 'Tanggal Daftar']);

            foreach ($enrollments as $enrollment) {
                fputcsv($file, [
                    $enrollment->user->name,
                    $enrollment->user->email,
                    $enrollment->user->phone ?? '-',
                    ucfirst($enrollment->status),
                    $enrollment->enrolled_at->format('d/m/Y H:i'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Remove student from course (cancel enrollment).
     */
    public function removeFromCourse(Enrollment $enrollment)
    {
        // Verify that the enrollment belongs to teacher's course
        if ($enrollment->course->instructor_id !== Auth::id()) {
            abort(403, 'Akses ditolak.');
        }

        $studentName = $enrollment->user->name;
        $courseName = $enrollment->course->title;

        $enrollment->update(['status' => 'cancelled']);

        return redirect()->back()
            ->with('success', "Siswa {$studentName} berhasil dikeluarkan dari kursus {$courseName}.");
    }

    /**
     * Get student statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_students' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->distinct('user_id')->count(),
            'active_enrollments' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->where('status', 'active')->count(),
            'completed_enrollments' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->where('status', 'completed')->count(),
            'recent_enrollments' => Enrollment::whereHas('course', function($q) {
                $q->where('instructor_id', Auth::id());
            })->where('enrolled_at', '>=', now()->subDays(7))->count(),
        ];

        return response()->json($stats);
    }
}
