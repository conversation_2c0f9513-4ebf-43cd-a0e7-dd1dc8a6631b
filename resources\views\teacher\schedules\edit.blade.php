@extends('layouts.app')

@section('title', 'Edit Jadwal - Sistem Kursus')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #FF3F33; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-edit me-2"></i>Edit Jadwal
                    </h2>
                    <p class="text-muted mb-0">Edit jadwal: {{ $schedule->title }}</p>
                </div>
                <a href="{{ route('teacher.schedules.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div class="card-header" style="background: linear-gradient(135deg, #075B5E 0%, #9FC87E 100%); border-radius: 15px 15px 0 0; border: none;">
                    <h5 class="mb-0 text-white" style="font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-calendar me-2"></i>Form Edit Jadwal
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('teacher.schedules.update', $schedule) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <!-- Course Selection -->
                        <div class="mb-3">
                            <label for="course_id" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-book me-1"></i>Kursus
                            </label>
                            <select class="form-select @error('course_id') is-invalid @enderror" 
                                    id="course_id" name="course_id" required>
                                <option value="">Pilih Kursus</option>
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}" 
                                            {{ (old('course_id', $schedule->course_id) == $course->id) ? 'selected' : '' }}>
                                        {{ $course->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('course_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-heading me-1"></i>Judul Jadwal
                            </label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $schedule->title) }}" 
                                   placeholder="Masukkan judul jadwal" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-file-text me-1"></i>Deskripsi (Opsional)
                            </label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" 
                                      placeholder="Deskripsi jadwal pembelajaran">{{ old('description', $schedule->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Date and Time -->
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="date" class="form-label fw-semibold" style="color: #075B5E;">
                                    <i class="fas fa-calendar me-1"></i>Tanggal
                                </label>
                                <input type="date" class="form-control @error('date') is-invalid @enderror" 
                                       id="date" name="date" value="{{ old('date', $schedule->date->format('Y-m-d')) }}" required>
                                @error('date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="start_time" class="form-label fw-semibold" style="color: #075B5E;">
                                    <i class="fas fa-clock me-1"></i>Waktu Mulai
                                </label>
                                <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                       id="start_time" name="start_time" value="{{ old('start_time', $schedule->start_time->format('H:i')) }}" required>
                                @error('start_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="end_time" class="form-label fw-semibold" style="color: #075B5E;">
                                    <i class="fas fa-clock me-1"></i>Waktu Selesai
                                </label>
                                <input type="time" class="form-control @error('end_time') is-invalid @enderror" 
                                       id="end_time" name="end_time" value="{{ old('end_time', $schedule->end_time->format('H:i')) }}" required>
                                @error('end_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Type -->
                        <div class="mb-3">
                            <label for="type" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-globe me-1"></i>Tipe Pembelajaran
                            </label>
                            <select class="form-select @error('type') is-invalid @enderror" 
                                    id="type" name="type" required>
                                <option value="">Pilih Tipe</option>
                                <option value="online" {{ old('type', $schedule->type) == 'online' ? 'selected' : '' }}>Online</option>
                                <option value="offline" {{ old('type', $schedule->type) == 'offline' ? 'selected' : '' }}>Offline</option>
                                <option value="hybrid" {{ old('type', $schedule->type) == 'hybrid' ? 'selected' : '' }}>Hybrid</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Location -->
                        <div class="mb-3" id="location-field">
                            <label for="location" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-map-marker-alt me-1"></i>Lokasi
                            </label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                   id="location" name="location" value="{{ old('location', $schedule->location) }}" 
                                   placeholder="Masukkan lokasi pembelajaran">
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Meeting Link -->
                        <div class="mb-4" id="meeting-link-field">
                            <label for="meeting_link" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-video me-1"></i>Link Meeting
                            </label>
                            <input type="url" class="form-control @error('meeting_link') is-invalid @enderror" 
                                   id="meeting_link" name="meeting_link" value="{{ old('meeting_link', $schedule->meeting_link) }}" 
                                   placeholder="https://zoom.us/j/... atau link meeting lainnya">
                            @error('meeting_link')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('teacher.schedules.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Update Jadwal
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control:focus, .form-select:focus {
    border-color: #9FC87E;
    box-shadow: 0 0 0 0.2rem rgba(159, 200, 126, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #FF3F33 0%, #FFE6E1 100%);
    border: none;
    border-radius: 8px;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e63529 0%, #ffd6d0 100%);
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border-radius: 8px;
}

.card {
    transition: all 0.3s ease;
}
</style>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const locationField = document.getElementById('location-field');
    const meetingLinkField = document.getElementById('meeting-link-field');
    const locationInput = document.getElementById('location');
    const meetingLinkInput = document.getElementById('meeting_link');
    
    function toggleFields() {
        const selectedType = typeSelect.value;
        
        if (selectedType === 'online') {
            locationField.style.display = 'none';
            meetingLinkField.style.display = 'block';
            locationInput.required = false;
            meetingLinkInput.required = true;
        } else if (selectedType === 'offline') {
            locationField.style.display = 'block';
            meetingLinkField.style.display = 'none';
            locationInput.required = true;
            meetingLinkInput.required = false;
        } else if (selectedType === 'hybrid') {
            locationField.style.display = 'block';
            meetingLinkField.style.display = 'block';
            locationInput.required = true;
            meetingLinkInput.required = true;
        } else {
            locationField.style.display = 'block';
            meetingLinkField.style.display = 'none';
            locationInput.required = false;
            meetingLinkInput.required = false;
        }
    }
    
    typeSelect.addEventListener('change', toggleFields);
    
    // Initialize on page load
    toggleFields();
    
    // Validate time
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    
    function validateTime() {
        if (startTimeInput.value && endTimeInput.value) {
            if (startTimeInput.value >= endTimeInput.value) {
                endTimeInput.setCustomValidity('Waktu selesai harus setelah waktu mulai');
            } else {
                endTimeInput.setCustomValidity('');
            }
        }
    }
    
    startTimeInput.addEventListener('change', validateTime);
    endTimeInput.addEventListener('change', validateTime);
});
</script>
@endpush
