<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    /**
     * Display reports dashboard.
     */
    public function index(Request $request)
    {
        // Date range filter
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        if (is_string($startDate)) {
            $startDate = Carbon::parse($startDate);
        }
        if (is_string($endDate)) {
            $endDate = Carbon::parse($endDate);
        }

        // Basic statistics
        $totalUsers = User::count();
        $totalCourses = Course::count();
        $totalEnrollments = Enrollment::count();
        $totalRevenue = Payment::where('status', 'verified')->sum('amount');

        // Monthly statistics
        $monthlyEnrollments = Enrollment::whereBetween('enrolled_at', [$startDate, $endDate])
            ->selectRaw('DATE_FORMAT(enrolled_at, "%Y-%m") as month, COUNT(*) as count')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $monthlyRevenue = Payment::where('status', 'verified')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, SUM(amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Popular courses
        $popularCourses = Course::withCount('enrollments')
            ->orderBy('enrollments_count', 'desc')
            ->take(10)
            ->get();

        // Recent activities
        $recentEnrollments = Enrollment::with(['user', 'course'])
            ->latest()
            ->take(10)
            ->get();

        $recentPayments = Payment::with(['user', 'course'])
            ->latest()
            ->take(10)
            ->get();

        // User growth
        $userGrowth = User::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, COUNT(*) as count')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Course performance
        $coursePerformance = Course::with('instructor')
            ->withCount('enrollments')
            ->withSum('payments', 'amount')
            ->orderBy('enrollments_count', 'desc')
            ->take(10)
            ->get();

        return view('admin.reports.index', compact(
            'totalUsers',
            'totalCourses', 
            'totalEnrollments',
            'totalRevenue',
            'monthlyEnrollments',
            'monthlyRevenue',
            'popularCourses',
            'recentEnrollments',
            'recentPayments',
            'userGrowth',
            'coursePerformance',
            'startDate',
            'endDate'
        ));
    }
}
