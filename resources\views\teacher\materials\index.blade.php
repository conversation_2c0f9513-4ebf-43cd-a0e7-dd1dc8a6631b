@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - Sistem Kursus')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #FF3F33; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-file-alt me-2"></i><PERSON><PERSON><PERSON>
                    </h2>
                    <p class="text-muted mb-0">Kelola materi pembelajaran untuk kursus Anda</p>
                </div>
                <a href="{{ route('teacher.materials.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i><PERSON><PERSON> Materi
                </a>
            </div>
        </div>
    </div>

    {{-- <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif --}}

    <!-- Materials by Course -->
    @forelse($courses as $course)
        <div class="card mb-4" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <div class="card-header" style="background: linear-gradient(135deg, #FF3F33 0%, #FFE6E1 100%); border-radius: 15px 15px 0 0; border: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 text-white" style="font-family: 'Poppins', sans-serif; font-weight: 600;">
                            {{ $course->title }}
                        </h5>
                        <small class="text-white opacity-75">
                            {{ $course->materials->count() }} materi
                        </small>
                    </div>
                    <a href="{{ route('teacher.materials.create', ['course_id' => $course->id]) }}" 
                       class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i>Tambah Materi
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @forelse($course->materials as $material)
                    <div class="d-flex align-items-center p-3 border-bottom">
                        <div class="me-3">
                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 40px; height: 40px; background: #FFE6E1; color: #FF3F33;">
                                <span class="fw-bold">{{ $material->order }}</span>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                {{ $material->title }}
                            </h6>
                            <p class="text-muted mb-0 small">
                                {{ Str::limit($material->content, 100) }}
                            </p>
                        </div>
                        <div class="ms-3">
                            <div class="btn-group" role="group">
                                <a href="{{ route('teacher.materials.show', $material) }}" 
                                   class="btn btn-outline-info btn-sm" title="Lihat">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('teacher.materials.edit', $material) }}" 
                                   class="btn btn-outline-warning btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('teacher.materials.destroy', $material) }}" 
                                      method="POST" class="d-inline delete-form">
                                    @csrf
                                    @method('DELETE')
                                    <button type="button" class="btn btn-outline-danger btn-sm delete-btn" 
                                            data-material-title="{{ $material->title }}" title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="fas fa-file-alt fa-3x text-muted"></i>
                        </div>
                        <h6 class="text-muted">Belum ada materi untuk kursus ini</h6>
                        <p class="text-muted small mb-3">Mulai tambahkan materi pembelajaran untuk siswa Anda</p>
                        <a href="{{ route('teacher.materials.create', ['course_id' => $course->id]) }}" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Tambah Materi Pertama
                        </a>
                    </div>
                @endforelse
            </div>
        </div>
    @empty
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-chalkboard fa-4x text-muted"></i>
            </div>
            <h4 class="text-muted mb-3">Belum Ada Kursus</h4>
            <p class="text-muted mb-4">Anda belum memiliki kursus. Hubungi admin untuk mendapatkan kursus yang dapat Anda ajar.</p>
            <a href="{{ route('teacher.courses.index') }}" class="btn btn-primary">
                <i class="fas fa-book me-2"></i>Lihat Kursus Saya
            </a>
        </div>
    @endforelse
</div>

<style>
.card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px;
}

.alert {
    border-radius: 12px;
    border: none;
}
</style>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete confirmation
    document.querySelectorAll('.delete-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const materialTitle = this.getAttribute('data-material-title');
            const form = this.closest('.delete-form');

            Swal.fire({
                title: 'Hapus Materi?',
                text: `Apakah Anda yakin ingin menghapus materi "${materialTitle}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#FF3F33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Ya, Hapus!',
                cancelButtonText: 'Batal',
                customClass: {
                    popup: 'rounded-3',
                    confirmButton: 'rounded-2',
                    cancelButton: 'rounded-2'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });
});
</script>
@endpush
