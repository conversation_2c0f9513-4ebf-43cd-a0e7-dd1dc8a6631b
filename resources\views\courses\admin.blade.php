@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - Sistem Kursus')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-600 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.75rem;">
                <i class="fas fa-cogs me-2"></i><PERSON><PERSON><PERSON>
            </h1>
            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Manajemen semua kursus yang tersedia</p>
        </div>
        <a href="{{ route('admin.courses.create') }}" class="btn fw-500"
           style="background: #FF3F33; color: white; border-radius: 8px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
            <i class="fas fa-plus me-2"></i>Tambah Kursus
        </a>
    </div>

    @if($courses->isEmpty())
    <!-- Empty State -->
    <div class="text-center py-5">
        <div class="mb-3">
            <div class="d-inline-flex align-items-center justify-content-center rounded-circle"
                 style="width: 80px; height: 80px; background: #FFE6E1;">
                <i class="fas fa-folder-open" style="color: #FF3F33; font-size: 1.8rem;"></i>
            </div>
        </div>
        <h3 class="fw-600 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.3rem;">Belum Ada Kursus</h3>
        <p class="text-muted mb-3" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Mulai dengan menambahkan kursus pertama Anda.</p>
        <a href="{{ route('admin.courses.create') }}" class="btn fw-500"
           style="background: #FF3F33; color: white; border-radius: 8px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: none;">
            <i class="fas fa-plus me-2"></i>Tambah Kursus Pertama
        </a>
    </div>
    @else
    <!-- Courses Table -->
    <div class="card shadow-sm border-0" style="border-radius: 12px;">
        <div class="card-header text-white" style="background: #075B5E; border-radius: 12px 12px 0 0;">
            <h5 class="card-title mb-0 fw-600" style="font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                <i class="fas fa-table me-2"></i>Daftar Kursus
            </h5>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: #f8f9fa;">
                        <tr>
                            <th class="border-0 fw-500 py-3" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.85rem;">#</th>
                            <th class="border-0 fw-500 py-3" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.85rem;">
                                <i class="fas fa-book me-1"></i>Judul
                            </th>
                            <th class="border-0 fw-500 py-3" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.85rem;">
                                <i class="fas fa-user-tie me-1"></i>Pengajar
                            </th>
                            <th class="border-0 fw-500 py-3" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.85rem;">
                                <i class="fas fa-tag me-1"></i>Harga
                            </th>
                            <th class="border-0 fw-500 py-3" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.85rem;">
                                <i class="fas fa-calendar me-1"></i>Tanggal Dibuat
                            </th>
                            <th class="border-0 fw-500 py-3 text-center" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.85rem;">
                                <i class="fas fa-cog me-1"></i>Aksi
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($courses as $index => $course)
                        <tr style="border-bottom: 1px solid #f1f3f4;">
                            <td class="align-middle py-3">
                                <span class="badge fw-500" style="background: #075B5E; color: white; font-family: 'Poppins', sans-serif; font-size: 0.75rem;">
                                    {{ $courses->firstItem() + $index }}
                                </span>
                            </td>
                            <td class="align-middle py-3">
                                <div class="fw-600 mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    {{ $course->title }}
                                </div>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    {{ Str::limit($course->description, 50) }}
                                </small>
                            </td>
                            <td class="align-middle py-3">
                                <span class="badge fw-500" style="background: #9FC87E; color: white; font-family: 'Poppins', sans-serif; font-size: 0.75rem;">
                                    {{ $course->instructor->name ?? 'Belum ditentukan' }}
                                </span>
                            </td>
                            <td class="align-middle py-3">
                                <span class="fw-600" style="color: #FF3F33; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">
                                    {{ $course->formatted_price }}
                                </span>
                            </td>
                            <td class="align-middle py-3">
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ $course->created_at->format('d/m/Y H:i') }}
                                </small>
                            </td>
                            <td class="align-middle text-center py-3">
                                <div class="btn-group" role="group">
                                    <!-- Edit Button -->
                                    <a href="{{ route('admin.courses.edit', $course) }}"
                                       class="btn btn-sm me-1"
                                       style="background: #FFE6E1; color: #FF3F33; border: 1px solid #FF3F33; border-radius: 6px; font-size: 0.8rem;"
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>

                                    <!-- Delete Button -->
                                    <form action="{{ route('admin.courses.destroy', $course) }}"
                                          method="POST"
                                          class="d-inline delete-form">
                                        @csrf
                                        @method('DELETE')
                                        <button type="button"
                                                class="btn btn-sm delete-btn"
                                                style="background: #FFE6E1; color: #FF3F33; border: 1px solid #FF3F33; border-radius: 6px; font-size: 0.8rem;"
                                                title="Hapus"
                                                data-course-title="{{ $course->title }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mt-4">
        <div class="col-md-3 mb-3">
            <div class="card text-white border-0 shadow-sm" style="background: #FF3F33; border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="fw-600 mb-1" style="font-family: 'Poppins', sans-serif; font-size: 1.5rem;">{{ $courses->total() }}</h4>
                            <p class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Total Kursus</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-book" style="font-size: 1.8rem; opacity: 0.7;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-white border-0 shadow-sm" style="background: #9FC87E; border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="fw-600 mb-1" style="font-family: 'Poppins', sans-serif; font-size: 1.5rem;">{{ $courses->where('price', '>', 0)->count() }}</h4>
                            <p class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Kursus Berbayar</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign" style="font-size: 1.8rem; opacity: 0.7;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-white border-0 shadow-sm" style="background: #075B5E; border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="fw-600 mb-1" style="font-family: 'Poppins', sans-serif; font-size: 1.5rem;">{{ $courses->where('price', 0)->count() }}</h4>
                            <p class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem;">Kursus Gratis</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-gift" style="font-size: 1.8rem; opacity: 0.7;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-white border-0 shadow-sm" style="background: #FFE6E1; border-radius: 12px;">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="fw-600 mb-1" style="font-family: 'Poppins', sans-serif; font-size: 1.2rem; color: #FF3F33;">Rp {{ number_format($courses->avg('price'), 0, ',', '.') }}</h4>
                            <p class="mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem; color: #075B5E;">Harga Rata-rata</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line" style="font-size: 1.8rem; opacity: 0.7; color: #FF3F33;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('styles')
<style>
    .btn:hover {
        transform: translateY(-1px);
        transition: all 0.2s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .fw-300 { font-weight: 300; }
    .fw-400 { font-weight: 400; }
    .fw-500 { font-weight: 500; }
    .fw-600 { font-weight: 600; }
    .fw-700 { font-weight: 700; }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 1rem;
        }

        .table-responsive {
            font-size: 0.8rem;
        }

        .btn-group {
            flex-direction: column;
            gap: 0.25rem;
        }

        .card-body h4 {
            font-size: 1.2rem !important;
        }

        .card-body p {
            font-size: 0.75rem !important;
        }
    }

    @media (max-width: 576px) {
        .container-fluid {
            padding: 0.5rem;
        }

        .table th, .table td {
            padding: 0.5rem 0.25rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete confirmation with modern styling
    document.querySelectorAll('.delete-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const courseTitle = this.getAttribute('data-course-title');
            const form = this.closest('.delete-form');

            Swal.fire({
                title: 'Konfirmasi Hapus',
                html: `Apakah Anda yakin ingin menghapus kursus:<br><strong style="color: #FF3F33;">"${courseTitle}"</strong>?`,
                icon: 'warning',
                iconColor: '#FF3F33',
                showCancelButton: true,
                confirmButtonColor: '#FF3F33',
                cancelButtonColor: '#075B5E',
                confirmButtonText: '<i class="fas fa-trash me-1"></i>Ya, Hapus!',
                cancelButtonText: '<i class="fas fa-times me-1"></i>Batal',
                reverseButtons: true,
                customClass: {
                    popup: 'rounded-3',
                    title: 'fw-600',
                    confirmButton: 'btn fw-500 rounded-2',
                    cancelButton: 'btn fw-500 rounded-2'
                },
                buttonsStyling: false,
                fontFamily: 'Poppins, sans-serif'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Menghapus...',
                        text: 'Sedang memproses permintaan Anda',
                        icon: 'info',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    form.submit();
                }
            });
        });
    });
});
</script>
@endpush

@endsection