@extends('layouts.app')

@section('title', 'Detail Pengguna - Admin')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="fw-600 mb-2" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.75rem;">
                <i class="fas fa-user me-2"></i>Detail Pengguna
            </h1>
            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Informasi lengkap pengguna {{ $user->name }}</p>
        </div>
        <a href="{{ route('admin.users.index') }}" class="btn fw-500"
           style="background: #FFE6E1; color: #3674B5; border-radius: 12px; padding: 0.6rem 1.2rem; font-family: 'Poppins', sans-serif; font-size: 0.9rem; border: 2px solid #3674B5;">
            <i class="fas fa-arrow-left me-1"></i>Kembali
        </a>
    </div>

    <div class="row">
        <!-- User Profile Card -->
        <div class="col-md-4 mb-4">
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-body text-center p-4">
                    <div class="rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 100px; height: 100px; background: linear-gradient(135deg, #3674B5, #FFE6E1); color: white; font-weight: 600; font-size: 2rem;">
                        {{ strtoupper(substr($user->name, 0, 1)) }}
                    </div>
                    <h4 class="fw-600 mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">{{ $user->name }}</h4>
                    <p class="text-muted mb-3" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $user->email }}</p>
                    
                    @if($user->role)
                        @if($user->role->name == 'Admin')
                            <span class="badge fw-500 mb-3" style="background: #3674B5; color: white; padding: 0.5rem 1rem; border-radius: 12px; font-size: 0.85rem;">
                                <i class="fas fa-user-shield me-1"></i>{{ $user->role->name }}
                            </span>
                        @elseif($user->role->name == 'Teacher')
                            <span class="badge fw-500 mb-3" style="background: #9FC87E; color: white; padding: 0.5rem 1rem; border-radius: 12px; font-size: 0.85rem;">
                                <i class="fas fa-chalkboard-teacher me-1"></i>{{ $user->role->name }}
                            </span>
                        @else
                            <span class="badge fw-500 mb-3" style="background: #075B5E; color: white; padding: 0.5rem 1rem; border-radius: 12px; font-size: 0.85rem;">
                                <i class="fas fa-user-graduate me-1"></i>{{ $user->role->name }}
                            </span>
                        @endif
                    @endif

                    <div class="row text-center mt-4">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="fw-600 mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                    @if($user->isTeacher())
                                        {{ $user->taughtCourses->count() }}
                                    @else
                                        {{ $user->enrollments->count() }}
                                    @endif
                                </h5>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    @if($user->isTeacher())
                                        Kursus Diajar
                                    @else
                                        Kursus Diikuti
                                    @endif
                                </small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h5 class="fw-600 mb-1" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                {{ $user->payments->count() }}
                            </h5>
                            <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">Pembayaran</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Info Card -->
            <div class="card border-0 mt-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-info-circle me-2"></i>Informasi
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="mb-3">
                        <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">EMAIL</label>
                        <p class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $user->email }}</p>
                    </div>
                    @if($user->phone)
                        <div class="mb-3">
                            <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">TELEPON</label>
                            <p class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $user->phone }}</p>
                        </div>
                    @endif
                    <div class="mb-3">
                        <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">BERGABUNG</label>
                        <p class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $user->created_at->format('d M Y H:i') }}</p>
                    </div>
                    <div class="mb-0">
                        <label class="fw-500 text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">STATUS</label>
                        <div>
                            @if($user->email_verified_at)
                                <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                    <i class="fas fa-check me-1"></i>Aktif
                                </span>
                            @else
                                <span class="badge fw-500" style="background: #3674B5; color: white; padding: 0.4rem 0.8rem; border-radius: 8px; font-size: 0.75rem;">
                                    <i class="fas fa-times me-1"></i>Nonaktif
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Cards -->
        <div class="col-md-8">
            @if($user->isTeacher())
                <!-- Courses Taught -->
                <div class="card border-0 mb-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                        <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                            <i class="fas fa-chalkboard-teacher me-2"></i>Kursus yang Diajar
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        @forelse($user->taughtCourses as $course)
                            <div class="d-flex align-items-center p-4 border-bottom">
                                <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 50px; height: 50px; background: #9FC87E; color: white;">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $course->title }}</h6>
                                    <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                        {{ $course->enrollments->count() }} siswa • {{ $course->formatted_price }}
                                    </small>
                                </div>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    {{ $course->created_at->format('M Y') }}
                                </small>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-chalkboard-teacher fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada kursus yang diajar</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            @else
                <!-- Enrollments -->
                <div class="card border-0 mb-4" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                    <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                        <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                            <i class="fas fa-user-graduate me-2"></i>Kursus yang Diikuti
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        @forelse($user->enrollments as $enrollment)
                            <div class="d-flex align-items-center p-4 border-bottom">
                                <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 50px; height: 50px; background: #075B5E; color: white;">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $enrollment->course->title }}</h6>
                                    <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                        Pengajar: {{ $enrollment->course->instructor->name }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    @if($enrollment->status == 'active')
                                        <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                            Aktif
                                        </span>
                                    @elseif($enrollment->status == 'completed')
                                        <span class="badge fw-500" style="background: #075B5E; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                            Selesai
                                        </span>
                                    @else
                                        <span class="badge fw-500" style="background: #3674B5; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                            Dibatalkan
                                        </span>
                                    @endif
                                    <br><small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.7rem;">
                                        {{ $enrollment->enrolled_at->format('d M Y') }}
                                    </small>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-user-graduate fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum mengikuti kursus apapun</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            @endif

            <!-- Payments -->
            <div class="card border-0" style="border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div class="card-header border-0 bg-white" style="border-radius: 16px 16px 0 0; padding: 1.5rem;">
                    <h5 class="mb-0 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 1.1rem;">
                        <i class="fas fa-credit-card me-2"></i>Riwayat Pembayaran
                    </h5>
                </div>
                <div class="card-body p-0">
                    @forelse($user->payments as $payment)
                        <div class="d-flex align-items-center p-4 border-bottom">
                            <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px; background: #6C5CE7; color: white;">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-600" style="color: #075B5E; font-family: 'Poppins', sans-serif; font-size: 0.9rem;">{{ $payment->course->title }}</h6>
                                <small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.8rem;">
                                    {{ $payment->payment_method }} • Rp {{ number_format($payment->amount, 0, ',', '.') }}
                                </small>
                            </div>
                            <div class="text-end">
                                @if($payment->status == 'verified')
                                    <span class="badge fw-500" style="background: #9FC87E; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                        Verified
                                    </span>
                                @elseif($payment->status == 'pending')
                                    <span class="badge fw-500" style="background: #FFA500; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                        Pending
                                    </span>
                                @else
                                    <span class="badge fw-500" style="background: #3674B5; color: white; padding: 0.3rem 0.6rem; border-radius: 6px; font-size: 0.7rem;">
                                        Rejected
                                    </span>
                                @endif
                                <br><small class="text-muted" style="font-family: 'Poppins', sans-serif; font-size: 0.7rem;">
                                    {{ $payment->created_at->format('d M Y') }}
                                </small>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0" style="font-family: 'Poppins', sans-serif; font-size: 0.9rem;">Belum ada riwayat pembayaran</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
