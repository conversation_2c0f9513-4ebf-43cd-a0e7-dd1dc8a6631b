<?php $__env->startSection('title', 'Tambah Materi - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #FF3F33; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-plus me-2"></i><PERSON><PERSON> Materi
                    </h2>
                    <p class="text-muted mb-0">Tambahkan materi pembelajaran baru untuk kursus Anda</p>
                </div>
                <a href="<?php echo e(route('teacher.materials.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i><PERSON><PERSON><PERSON>
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                <div class="card-header" style="background: linear-gradient(135deg, #075B5E 0%, #9FC87E 100%); border-radius: 15px 15px 0 0; border: none;">
                    <h5 class="mb-0 text-white" style="font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-file-alt me-2"></i>Form Tambah Materi
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="<?php echo e(route('teacher.materials.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Course Selection -->
                        <div class="mb-3">
                            <label for="course_id" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-book me-1"></i>Kursus
                            </label>
                            <select class="form-select <?php $__errorArgs = ['course_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="course_id" name="course_id" required>
                                <option value="">Pilih Kursus</option>
                                <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($course->id); ?>" 
                                            <?php echo e((old('course_id', $selectedCourse?->id) == $course->id) ? 'selected' : ''); ?>>
                                        <?php echo e($course->title); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['course_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-heading me-1"></i>Judul Materi
                            </label>
                            <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="title" name="title" value="<?php echo e(old('title')); ?>" 
                                   placeholder="Masukkan judul materi" required>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Order -->
                        <div class="mb-3">
                            <label for="order" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-sort-numeric-up me-1"></i>Urutan Materi
                            </label>
                            <input type="number" class="form-control <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="order" name="order" value="<?php echo e(old('order', 1)); ?>" 
                                   min="1" placeholder="Urutan materi (1, 2, 3, ...)" required>
                            <?php $__errorArgs = ['order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Jika urutan sudah ada, materi lain akan digeser otomatis
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="mb-4">
                            <label for="content" class="form-label fw-semibold" style="color: #075B5E;">
                                <i class="fas fa-file-text me-1"></i>Konten Materi
                            </label>
                            <textarea class="form-control <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="content" name="content" rows="8" 
                                      placeholder="Masukkan konten materi (bisa berupa teks, link video, link PDF, dll.)" required><?php echo e(old('content')); ?></textarea>
                            <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">
                                <i class="fas fa-lightbulb me-1"></i>
                                Tips: Anda bisa menambahkan link YouTube, Google Drive, atau teks pembelajaran
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('teacher.materials.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Simpan Materi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control:focus, .form-select:focus {
    border-color: #9FC87E;
    box-shadow: 0 0 0 0.2rem rgba(159, 200, 126, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #FF3F33 0%, #FFE6E1 100%);
    border: none;
    border-radius: 8px;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #e63529 0%, #ffd6d0 100%);
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border-radius: 8px;
}

.card {
    transition: all 0.3s ease;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-suggest order based on selected course
    const courseSelect = document.getElementById('course_id');
    const orderInput = document.getElementById('order');
    
    courseSelect.addEventListener('change', function() {
        if (this.value) {
            // You could make an AJAX call here to get the next order number
            // For now, we'll just reset to 1
            if (!orderInput.value || orderInput.value == 1) {
                orderInput.value = 1;
            }
        }
    });
    
    // Character counter for content
    const contentTextarea = document.getElementById('content');
    const maxLength = 5000;
    
    // Create character counter
    const counterDiv = document.createElement('div');
    counterDiv.className = 'form-text text-end';
    counterDiv.innerHTML = `<span id="char-count">0</span>/${maxLength} karakter`;
    contentTextarea.parentNode.appendChild(counterDiv);
    
    contentTextarea.addEventListener('input', function() {
        const currentLength = this.value.length;
        document.getElementById('char-count').textContent = currentLength;
        
        if (currentLength > maxLength * 0.9) {
            counterDiv.classList.add('text-warning');
        } else {
            counterDiv.classList.remove('text-warning');
        }
        
        if (currentLength > maxLength) {
            counterDiv.classList.add('text-danger');
            counterDiv.classList.remove('text-warning');
        } else {
            counterDiv.classList.remove('text-danger');
        }
    });
    
    // Trigger initial count
    contentTextarea.dispatchEvent(new Event('input'));
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views\teacher\materials\create.blade.php ENDPATH**/ ?>