<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\User;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get teacher users
        $teachers = User::whereHas('role', function($query) {
            $query->where('name', 'Teacher');
        })->get();

        if ($teachers->isEmpty()) {
            $this->command->warn('No teachers found. Please run AdminUserSeeder first.');
            return;
        }

        $courses = [
            [
                'title' => 'Laravel untuk Pemula',
                'description' => 'Pelajari framework Laravel dari dasar hingga mahir. Kursus ini mencakup routing, controller, model, view, database migration, dan fitur-fitur canggih Laravel lainnya.',
                'instructor_id' => $teachers->first()->id,
                'price' => 299000,
            ],
            [
                'title' => 'React.js Fundamental',
                'description' => 'Kuasai React.js untuk membangun aplikasi web modern. Mulai dari component, state management, hooks, hingga integrasi dengan API.',
                'instructor_id' => $teachers->count() > 1 ? $teachers->skip(1)->first()->id : $teachers->first()->id,
                'price' => 450000,
            ],
            [
                'title' => 'HTML & CSS Dasar',
                'description' => 'Pelajari dasar-dasar web development dengan HTML dan CSS. Cocok untuk pemula yang ingin memulai karir sebagai web developer.',
                'instructor_id' => $teachers->first()->id,
                'price' => 500000,
            ],
            [
                'title' => 'Database MySQL',
                'description' => 'Pelajari manajemen database MySQL dari basic query hingga optimasi performa. Termasuk stored procedure, trigger, dan indexing.',
                'instructor_id' => $teachers->count() > 1 ? $teachers->skip(1)->first()->id : $teachers->first()->id,
                'price' => 350000,
            ],
            [
                'title' => 'JavaScript ES6+',
                'description' => 'Menguasai JavaScript modern dengan ES6+ features. Arrow functions, destructuring, async/await, modules, dan banyak lagi.',
                'instructor_id' => $teachers->first()->id,
                'price' => 275000,
            ],
            [
                'title' => 'Python untuk Data Science',
                'description' => 'Belajar Python untuk analisis data dan machine learning. Menggunakan library pandas, numpy, matplotlib, dan scikit-learn.',
                'instructor_id' => $teachers->first()->id,
                'price' => 525000,
            ],
            [
                'title' => 'Git & GitHub',
                'description' => 'Pelajari version control dengan Git dan kolaborasi menggunakan GitHub. Essential skill untuk setiap programmer.',
                'instructor_id' => $teachers->count() > 1 ? $teachers->skip(1)->first()->id : $teachers->first()->id,
                'price' => 250000,
            ],
            [
                'title' => 'Vue.js Complete Guide',
                'description' => 'Framework JavaScript yang mudah dipelajari untuk membangun user interface yang reaktif dan modern.',
                'instructor_id' => $teachers->first()->id,
                'price' => 399000,
            ],
            [
                'title' => 'Node.js & Express',
                'description' => 'Bangun REST API dan web application menggunakan Node.js dan Express.js. Termasuk authentication dan database integration.',
                'instructor_id' => $teachers->count() > 1 ? $teachers->skip(1)->first()->id : $teachers->first()->id,
                'price' => 425000,
            ],
            [
                'title' => 'UI/UX Design Principles',
                'description' => 'Pelajari prinsip-prinsip design yang baik untuk menciptakan user experience yang optimal. Dari wireframing hingga prototyping.',
                'instructor_id' => $teachers->first()->id,
                'price' => 375000,
            ],
        ];

        foreach ($courses as $course) {
            Course::create($course);
        }

    }
}