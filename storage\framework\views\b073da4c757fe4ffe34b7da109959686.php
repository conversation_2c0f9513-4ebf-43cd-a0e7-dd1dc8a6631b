

<?php $__env->startSection('title', 'Tambah Kursus - Sistem Kursus'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    <!-- Header Sistem Kursus - Menggunakan desain dari gambar pertama -->
    <div class="bg-primary text-white py-3 px-4 rounded-3 mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-graduation-cap me-3 fs-4"></i>
            <h1 class="h4 mb-0 fw-bold">Sistem Kursus</h1>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-12 col-md-10 col-lg-8 col-xl-6">
            <!-- Header Section untuk Form -->
            <div class="mb-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-success rounded-circle p-3 me-3">
                        <i class="fas fa-plus text-white"></i>
                    </div>
                    <div>
                        <h2 class="h3 mb-1 text-dark fw-bold">Tambah Kursus Baru</h2>
                        <p class="text-muted mb-0">Lengkapi informasi kursus di bawah ini</p>
                    </div>
                </div>
            </div>

            <!-- Main Form Card - Menggunakan gaya card dari gambar kedua, dengan border dan shadow -->
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4 p-md-5">
                    <form action="<?php echo e(route('admin.courses.store')); ?>" method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_field(); ?>
                        
                        <!-- Judul Kursus -->
                        <div class="mb-4">
                            <label for="title" class="form-label fw-semibold text-dark mb-2">
                                <i class="fas fa-book-open text-primary me-2"></i>Judul Kursus
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg border-2 <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="title" 
                                   name="title" 
                                   value="<?php echo e(old('title')); ?>" 
                                   placeholder="Contoh: Belajar Laravel untuk Pemula"
                                   required>
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Gunakan judul yang menarik dan deskriptif
                            </div>
                        </div>

                        <!-- Pengajar -->
                        <div class="mb-4">
                            <label for="instructor_id" class="form-label fw-semibold text-dark mb-2">
                                <i class="fas fa-chalkboard-teacher text-primary me-2"></i>Pengajar
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select form-select-lg border-2 <?php $__errorArgs = ['instructor_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    id="instructor_id"
                                    name="instructor_id"
                                    required>
                                <option value="">Pilih Pengajar</option>
                                <?php $__currentLoopData = $teachers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($teacher->id); ?>" <?php echo e(old('instructor_id') == $teacher->id ? 'selected' : ''); ?>>
                                        <?php echo e($teacher->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['instructor_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Pilih pengajar yang akan mengampu kursus ini
                            </div>
                        </div>

                        <!-- Deskripsi -->
                        <div class="mb-4">
                            <label for="description" class="form-label fw-semibold text-dark mb-2">
                                <i class="fas fa-align-left text-primary me-2"></i>Deskripsi Kursus
                                <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control border-2 <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="description" 
                                      name="description" 
                                      rows="5" 
                                      placeholder="Jelaskan secara detail apa yang akan dipelajari dalam kursus ini, siapa target audiensnya, dan manfaat yang akan didapat..."
                                      required><?php echo e(old('description')); ?></textarea>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text text-muted">
                                <i class="fas fa-lightbulb me-1"></i>
                                Deskripsi yang baik akan membantu calon peserta memahami value dari kursus Anda
                            </div>
                        </div>

                        <!-- Harga -->
                        <div class="mb-5">
                            <label for="price" class="form-label fw-semibold text-dark mb-2">
                                <i class="fas fa-tag text-primary me-2"></i>Harga Kursus
                                <span class="text-danger">*</span>
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-light border-2 fw-semibold">
                                    <i class="fas fa-rupiah-sign text-success me-1"></i>Rp
                                </span>
                                <input type="number" 
                                       class="form-control border-2 <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="price" 
                                       name="price" 
                                       value="<?php echo e(old('price')); ?>" 
                                       placeholder="500000"
                                       min="0"
                                       step="1000"
                                       required>
                                <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-flex align-items-center">
                                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="form-text text-muted">
                                <i class="fas fa-calculator me-1"></i>
                                Tentukan harga yang kompetitif sesuai dengan nilai kursus
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex flex-column flex-sm-row gap-3 pt-3 border-top">
                            <button type="submit" class="btn btn-primary btn-lg flex-fill">
                                <i class="fas fa-save me-2"></i>Simpan Kursus
                            </button>
                            <a href="<?php echo e(route('admin.courses.index')); ?>" class="btn btn-outline-secondary btn-lg flex-fill">
                                <i class="fas fa-arrow-left me-2"></i>Kembali
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    }
    
    .form-control.border-2 {
        border-width: 2px !important;
    }
    
    .input-group-text {
        border-width: 2px;
    }
    
    .card {
        transition: all 0.3s ease;
    }
    
    .btn {
        transition: all 0.2s ease;
    }
    
    .btn:hover {
        transform: translateY(-1px);
    }
    
    .form-label {
        font-size: 0.95rem;
    }
    
    @media (max-width: 576px) {
        .card-body {
            padding: 1.5rem !important;
        }
        
        .input-group-lg .form-control,
        .input-group-lg .input-group-text {
            font-size: 1rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Price formatting - memastikan hanya angka yang bisa diinput
    document.getElementById('price').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // Hapus semua non-digit
        if (value) {
            e.target.value = value;
        }
    });

    // Auto-resize textarea untuk deskripsi
    document.getElementById('description').addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\sistem-kursus\resources\views\courses\create.blade.php ENDPATH**/ ?>