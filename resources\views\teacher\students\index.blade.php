@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> - Sistem Kursus')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1" style="color: #3674B5; font-family: 'Poppins', sans-serif; font-weight: 600;">
                        <i class="fas fa-users me-2"></i>Ke<PERSON><PERSON> Siswa
                    </h2>
                    <p class="text-muted mb-0">Ke<PERSON>la siswa yang terdaftar dalam kursus Anda</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: #578FCA;">
                <div class="card-body text-white">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ $stats['total_students'] }}</h4>
                    <small>Total Siswa</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: linear-gradient(135deg, #9FC87E 0%, #e8f5e8 100%);">
                <div class="card-body text-white">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ $stats['active_students'] }}</h4>
                    <small>Siswa Aktif</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: linear-gradient(135deg, #075B5E 0%, #9FC87E 100%);">
                <div class="card-body text-white">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ $stats['completed_students'] }}</h4>
                    <small>Siswa Lulus</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1); background: linear-gradient(135deg, #6c757d 0%, #f8f9fa 100%);">
                <div class="card-body text-white">
                    <i class="fas fa-user-times fa-2x mb-2"></i>
                    <h4 class="mb-1">{{ $stats['cancelled_students'] }}</h4>
                    <small>Siswa Batal</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Filters -->
    <div class="card mb-4" style="border-radius: 15px; border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <div class="card-body">
            <form method="GET" action="{{ route('teacher.students.index') }}">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="course_id" class="form-label small fw-semibold">Kursus</label>
                        <select class="form-select form-select-sm" id="course_id" name="course_id">
                            <option value="">Semua Kursus</option>
                            @foreach($courses as $course)
                                <option value="{{ $course->id }}" {{ request('course_id') == $course->id ? 'selected' : '' }}>
                                    {{ $course->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label small fw-semibold">Status</label>
                        <select class="form-select form-select-sm" id="status" name="status">
                            <option value="">Semua Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label small fw-semibold">Cari Siswa</label>
                        <input type="text" class="form-control form-control-sm" id="search" name="search" 
                               value="{{ request('search') }}" placeholder="Nama atau email siswa">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2 w-100">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="{{ route('teacher.students.index') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-undo me-1"></i>Reset
                            </a>
                            
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Students List -->
    <div class="card" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <div class="card-body p-0">
            @forelse($enrollments as $enrollment)
                <div class="d-flex align-items-center p-4 border-bottom student-item">
                    <div class="me-3">
                        <div class="rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 50px; height: 50px; background: #FFE6E1; color: #3674B5; font-weight: bold;">
                            {{ strtoupper(substr($enrollment->user->name, 0, 2)) }}
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0" style="color: #075B5E; font-family: 'Poppins', sans-serif;">
                                {{ $enrollment->user->name }}
                            </h6>
                            <span class="badge {{ $enrollment->status == 'active' ? 'bg-success' : ($enrollment->status == 'completed' ? 'bg-primary' : 'bg-secondary') }}">
                                {{ ucfirst($enrollment->status) }}
                            </span>
                        </div>
                        <p class="text-muted mb-1 small">{{ $enrollment->user->email }}</p>
                        <p class="text-muted mb-2 small">{{ $enrollment->course->title }}</p>
                        <div class="d-flex align-items-center text-muted small">
                            <i class="fas fa-calendar me-1"></i>
                            Daftar: {{ $enrollment->enrolled_at->format('d M Y') }}
                            @if($enrollment->user->phone)
                                <span class="mx-2">•</span>
                                <i class="fas fa-phone me-1"></i>
                                {{ $enrollment->user->phone }}
                            @endif
                        </div>
                    </div>
                    <div class="ms-3">
                        <div class="btn-group" role="group">
                            <a href="{{ route('teacher.students.show', $enrollment->user) }}" 
                               class="btn btn-outline-info btn-sm" title="Lihat Detail">
                                <i class="fas fa-eye"></i>
                            </a>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown" title="Ubah Status">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    @if($enrollment->status != 'active')
                                        <li>
                                            <form action="{{ route('teacher.enrollments.update-status', $enrollment) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="active">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-check-circle text-success me-2"></i>Aktifkan
                                                </button>
                                            </form>
                                        </li>
                                    @endif
                                    @if($enrollment->status != 'completed')
                                        <li>
                                            <form action="{{ route('teacher.enrollments.update-status', $enrollment) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="completed">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-graduation-cap text-primary me-2"></i>Tandai Selesai
                                                </button>
                                            </form>
                                        </li>
                                    @endif
                                    @if($enrollment->status != 'cancelled')
                                        <li>
                                            <form action="{{ route('teacher.enrollments.update-status', $enrollment) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('PATCH')
                                                <input type="hidden" name="status" value="cancelled">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-times-circle text-danger me-2"></i>Batalkan
                                                </button>
                                            </form>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-user-slash fa-3x text-muted"></i>
                    </div>
                    <h6 class="text-muted">Belum ada siswa terdaftar</h6>
                    <p class="text-muted small mb-3">Siswa akan muncul di sini setelah mendaftar kursus Anda</p>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Pagination -->
    @if($enrollments->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $enrollments->links() }}
        </div>
    @endif
</div>

<style>
.student-item:hover {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.btn-group .btn {
    border-radius: 8px !important;
    margin: 0 2px;
}

.alert {
    border-radius: 12px;
    border: none;
}

.card {
    transition: all 0.3s ease;
}
</style>
@endsection
