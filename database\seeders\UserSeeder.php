<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get Admin role ID
        $adminRoleId = DB::table('roles')->where('name', 'Admin')->first()->id;

        // Create admin user
        DB::table('users')->insert([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $adminRoleId,
            'phone' => '081234567890',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create sample teacher
        $teacherRoleId = DB::table('roles')->where('name', 'Teacher')->first()->id;

        DB::table('users')->insert([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $teacherRoleId,
            'phone' => '081234567891',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create sample student
        $studentRoleId = DB::table('roles')->where('name', 'Student')->first()->id;

        DB::table('users')->insert([
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $studentRoleId,
            'phone' => '081234567892',
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
