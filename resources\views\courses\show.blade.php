@extends('layouts.app')

@section('title', $course->title . ' - Sistem Kursus')

@section('content')
<div class="container">
    <!-- Course Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <nav aria-label="breadcrumb" class="mb-3">
                                <ol class="breadcrumb text-white-50">
                                    <li class="breadcrumb-item">
                                        <a href="{{ route('courses.index') }}" class="text-white-50 text-decoration-none">
                                            <i class="fas fa-book me-1"></i>Kursus
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active text-white" aria-current="page">
                                        {{ Str::limit($course->title, 30) }}
                                    </li>
                                </ol>
                            </nav>
                            <h1 class="h2 mb-2">{{ $course->title }}</h1>
                            <p class="mb-2">
                                <i class="fas fa-user me-2"></i>
                                Pengajar: <strong>{{ $course->instructor->name ?? 'Belum ditentukan' }}</strong>
                            </p>
                            <div class="d-flex align-items-center">
                                @if($course->reviews_count > 0)
                                    <div class="text-warning me-3">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star{{ $i <= $course->average_rating ? '' : '-o' }}"></i>
                                        @endfor
                                        <span class="ms-1">{{ number_format($course->average_rating, 1) }}</span>
                                    </div>
                                    <small class="text-white-50">
                                        ({{ $course->reviews_count }} review{{ $course->reviews_count > 1 ? 's' : '' }})
                                    </small>
                                @else
                                    <small class="text-white-50">Belum ada review</small>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <span class="badge bg-light text-primary fs-4 px-3 py-2">
                                    {{ $course->formatted_price }}
                                </span>
                            </div>
                            <small class="text-white-50">
                                <i class="fas fa-users me-1"></i>
                                {{ $course->total_students }} siswa terdaftar
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8 mb-4">
            <!-- Course Description -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Deskripsi Kursus
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ $course->description }}</p>
                </div>
            </div>

            <!-- Course Materials -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Materi Pembelajaran
                    </h5>
                    <span class="badge bg-primary">{{ $course->materials->count() }} materi</span>
                </div>
                <div class="card-body">
                    @forelse($course->materials()->orderBy('order')->get() as $material)
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="bg-light rounded-circle p-2 me-3">
                                <i class="fas fa-play text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $material->title }}</h6>
                                <small class="text-muted">{{ Str::limit($material->content, 100) }}</small>
                            </div>
                            <div>
                                <span class="badge bg-light text-dark">{{ $material->order }}</span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-3">
                            <i class="fas fa-file-alt fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Materi pembelajaran belum tersedia</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Course Schedule -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>Jadwal Kursus
                    </h5>
                    <span class="badge bg-info">{{ $course->schedules->count() }} jadwal</span>
                </div>
                <div class="card-body">
                    @forelse($course->schedules()->orderBy('date')->get() as $schedule)
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <div class="bg-light rounded-circle p-2 me-3">
                                <i class="fas fa-clock text-info"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $schedule->title }}</h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>{{ $schedule->date->format('d M Y') }}
                                    <i class="fas fa-clock ms-2 me-1"></i>{{ $schedule->start_time }} - {{ $schedule->end_time }}
                                </small>
                                @if($schedule->location)
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ $schedule->location }}
                                    </small>
                                @endif
                            </div>
                            <div>
                                <span class="badge bg-{{ $schedule->type == 'online' ? 'success' : 'primary' }}">
                                    {{ ucfirst($schedule->type) }}
                                </span>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-3">
                            <i class="fas fa-calendar fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Jadwal kursus belum tersedia</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Reviews Section -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Review & Rating
                    </h5>
                    @auth
                        @if(auth()->user()->enrollments()->where('course_id', $course->id)->exists() && 
                            !auth()->user()->reviews()->where('course_id', $course->id)->exists())
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#reviewModal">
                                <i class="fas fa-plus me-1"></i>Tulis Review
                            </button>
                        @endif
                    @endauth
                </div>
                <div class="card-body">
                    @forelse($course->reviews()->with('user')->latest()->take(5)->get() as $review)
                        <div class="mb-3 pb-3 border-bottom">
                            <div class="d-flex align-items-center mb-2">
                                <div class="bg-primary rounded-circle p-2 me-3">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $review->user->name }}</h6>
                                    <div class="text-warning">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star{{ $i <= $review->rating ? '' : '-o' }}"></i>
                                        @endfor
                                    </div>
                                </div>
                                <small class="text-muted">{{ $review->created_at->diffForHumans() }}</small>
                            </div>
                            @if($review->review)
                                <p class="mb-0 text-muted">{{ $review->review }}</p>
                            @endif
                        </div>
                    @empty
                        <div class="text-center py-3">
                            <i class="fas fa-star fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Belum ada review untuk kursus ini</p>
                        </div>
                    @endforelse

                    @if($course->reviews_count > 5)
                        <div class="text-center">
                            <a href="{{ route('courses.reviews.index', $course) }}" class="btn btn-outline-primary">
                                Lihat Semua Review ({{ $course->reviews_count }})
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Enrollment Card -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    @auth
                        @if(auth()->user()->isStudent())
                            @php
                                $isEnrolled = auth()->user()->enrollments()->where('course_id', $course->id)->exists();
                                $hasPaid = auth()->user()->payments()->where('course_id', $course->id)->where('status', 'verified')->exists();
                            @endphp

                            @if($isEnrolled)
                                @if($hasPaid)
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Anda sudah terdaftar dan telah membayar kursus ini
                                    </div>
                                @else
                                    <div class="alert alert-warning">
                                        <i class="fas fa-clock me-2"></i>
                                        Anda sudah terdaftar. Silakan lakukan pembayaran.
                                    </div>
                                    <a href="{{ route('student.payments.create', $course) }}" class="btn btn-warning w-100">
                                        <i class="fas fa-credit-card me-2"></i>Bayar Sekarang
                                    </a>
                                @endif
                            @else
                                <h5 class="text-primary mb-3">{{ $course->formatted_price }}</h5>
                                <form action="{{ route('student.courses.enroll', $course) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                        <i class="fas fa-user-plus me-2"></i>Daftar Kursus
                                    </button>
                                </form>
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Garansi 30 hari uang kembali
                                </small>
                            @endif
                        @elseif(auth()->user()->isTeacher())
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Anda adalah pengajar. Pengajar tidak dapat mendaftar sebagai siswa.
                            </div>
                            <button class="btn btn-secondary btn-lg w-100 mb-3" disabled>
                                <i class="fas fa-chalkboard-teacher me-2"></i>Anda Pengajar
                            </button>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Anda adalah admin. Admin tidak dapat mendaftar sebagai siswa.
                            </div>
                            <button class="btn btn-secondary btn-lg w-100 mb-3" disabled>
                                <i class="fas fa-user-shield me-2"></i>Anda Admin
                            </button>
                        @endif
                    @else
                        <h5 class="text-primary mb-3">{{ $course->formatted_price }}</h5>
                        <a href="{{ route('login') }}" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i>Login untuk Daftar
                        </a>
                        <small class="text-muted">
                            Belum punya akun? 
                            <a href="{{ route('register') }}" class="text-primary">Daftar di sini</a>
                        </small>
                    @endauth
                </div>
            </div>

            <!-- Course Info -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info me-2"></i>Informasi Kursus
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h6>{{ $course->total_students }}</h6>
                            <small class="text-muted">Siswa</small>
                        </div>
                        <div class="col-6 mb-3">
                            <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                            <h6>{{ $course->materials->count() }}</h6>
                            <small class="text-muted">Materi</small>
                        </div>
                        <div class="col-6">
                            <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                            <h6>{{ $course->schedules->count() }}</h6>
                            <small class="text-muted">Jadwal</small>
                        </div>
                        <div class="col-6">
                            <i class="fas fa-star fa-2x text-warning mb-2"></i>
                            <h6>{{ number_format($course->average_rating, 1) }}</h6>
                            <small class="text-muted">Rating</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Review Modal -->
@auth
@if(auth()->user()->enrollments()->where('course_id', $course->id)->exists() && 
    !auth()->user()->reviews()->where('course_id', $course->id)->exists())
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-star me-2"></i>Tulis Review
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('student.courses.review', $course) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Rating</label>
                        <div class="rating-input">
                            @for($i = 1; $i <= 5; $i++)
                                <input type="radio" name="rating" value="{{ $i }}" id="star{{ $i }}" required>
                                <label for="star{{ $i }}" class="star">
                                    <i class="fas fa-star"></i>
                                </label>
                            @endfor
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="review" class="form-label">Review (Opsional)</label>
                        <textarea class="form-control" id="review" name="review" rows="4" 
                                  placeholder="Bagikan pengalaman Anda mengikuti kursus ini..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>Kirim Review
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endauth

<style>
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: center;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #ddd;
    transition: color 0.2s;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #ffc107;
}
</style>
@endsection
